import{f as X,h as Be,r as D,j as v,d as Ue,B as Y,C as Z,i as J,k as Q,l as ee,n as ze}from"./globals.js";/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=X("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xe=X("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=X("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=X("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/*!
 * iro.js v5.5.2
 * 2016-2021 James Daniel
 * Licensed under MPL 2.0
 * github.com/jaames/iro.js
 */var x,U,ke,ie,Ee,j={},le=[],Ve=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;function R(t,r){for(var n in r)t[n]=r[n];return t}function Se(t){var r=t.parentNode;r&&r.removeChild(t)}function b(t,r,n){var e,i,a,o,s=arguments;if(r=R({},r),arguments.length>3)for(n=[n],e=3;e<arguments.length;e++)n.push(s[e]);if(n!=null&&(r.children=n),t!=null&&t.defaultProps!=null)for(i in t.defaultProps)r[i]===void 0&&(r[i]=t.defaultProps[i]);return o=r.key,(a=r.ref)!=null&&delete r.ref,o!=null&&delete r.key,ae(t,r,o,a)}function ae(t,r,n,e){var i={type:t,props:r,key:n,ref:e,__k:null,__p:null,__b:0,__e:null,l:null,__c:null,constructor:void 0};return x.vnode&&x.vnode(i),i}function G(t){return t.children}function qe(t){if(t==null||typeof t=="boolean")return null;if(typeof t=="string"||typeof t=="number")return ae(null,t,null,null);if(t.__e!=null||t.__c!=null){var r=ae(t.type,t.props,t.key,null);return r.__e=t.__e,r}return t}function W(t,r){this.props=t,this.context=r}function z(t,r){if(r==null)return t.__p?z(t.__p,t.__p.__k.indexOf(t)+1):null;for(var n;r<t.__k.length;r++)if((n=t.__k[r])!=null&&n.__e!=null)return n.__e;return typeof t.type=="function"?z(t):null}function Ce(t){var r,n;if((t=t.__p)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if((n=t.__k[r])!=null&&n.__e!=null){t.__e=t.__c.base=n.__e;break}return Ce(t)}}function te(t){(!t.__d&&(t.__d=!0)&&U.push(t)===1||ie!==x.debounceRendering)&&(ie=x.debounceRendering,(x.debounceRendering||ke)(Ke))}function Ke(){var t,r,n,e,i,a,o,s;for(U.sort(function(c,h){return h.__v.__b-c.__v.__b});t=U.pop();)t.__d&&(n=void 0,e=void 0,a=(i=(r=t).__v).__e,o=r.__P,s=r.u,r.u=!1,o&&(n=[],e=he(o,i,R({},i),r.__n,o.ownerSVGElement!==void 0,null,n,s,a??z(i)),Ne(n,i),e!=a&&Ce(i)))}function Ie(t,r,n,e,i,a,o,s,c){var h,u,l,d,f,y,m,p=n&&n.__k||le,_=p.length;if(s==j&&(s=a!=null?a[0]:_?z(n,0):null),h=0,r.__k=ce(r.__k,function(g){if(g!=null){if(g.__p=r,g.__b=r.__b+1,(l=p[h])===null||l&&g.key==l.key&&g.type===l.type)p[h]=void 0;else for(u=0;u<_;u++){if((l=p[u])&&g.key==l.key&&g.type===l.type){p[u]=void 0;break}l=null}if(d=he(t,g,l=l||j,e,i,a,o,null,s,c),(u=g.ref)&&l.ref!=u&&(m||(m=[])).push(u,g.__c||d,g),d!=null){if(y==null&&(y=d),g.l!=null)d=g.l,g.l=null;else if(a==l||d!=s||d.parentNode==null){e:if(s==null||s.parentNode!==t)t.appendChild(d);else{for(f=s,u=0;(f=f.nextSibling)&&u<_;u+=2)if(f==d)break e;t.insertBefore(d,s)}r.type=="option"&&(t.value="")}s=d.nextSibling,typeof r.type=="function"&&(r.l=d)}}return h++,g}),r.__e=y,a!=null&&typeof r.type!="function")for(h=a.length;h--;)a[h]!=null&&Se(a[h]);for(h=_;h--;)p[h]!=null&&je(p[h],p[h]);if(m)for(h=0;h<m.length;h++)Re(m[h],m[++h],m[++h])}function ce(t,r,n){if(n==null&&(n=[]),t==null||typeof t=="boolean")r&&n.push(r(null));else if(Array.isArray(t))for(var e=0;e<t.length;e++)ce(t[e],r,n);else n.push(r?r(qe(t)):t);return n}function Ye(t,r,n,e,i){var a;for(a in n)a in r||_e(t,a,null,n[a],e);for(a in r)i&&typeof r[a]!="function"||a==="value"||a==="checked"||n[a]===r[a]||_e(t,a,r[a],n[a],e)}function ge(t,r,n){r[0]==="-"?t.setProperty(r,n):t[r]=typeof n=="number"&&Ve.test(r)===!1?n+"px":n??""}function _e(t,r,n,e,i){var a,o,s,c,h;if(!((r=i?r==="className"?"class":r:r==="class"?"className":r)==="key"||r==="children"))if(r==="style")if(a=t.style,typeof n=="string")a.cssText=n;else{if(typeof e=="string"&&(a.cssText="",e=null),e)for(o in e)n&&o in n||ge(a,o,"");if(n)for(s in n)e&&n[s]===e[s]||ge(a,s,n[s])}else r[0]==="o"&&r[1]==="n"?(c=r!==(r=r.replace(/Capture$/,"")),h=r.toLowerCase(),r=(h in t?h:r).slice(2),n?(e||t.addEventListener(r,me,c),(t.t||(t.t={}))[r]=n):t.removeEventListener(r,me,c)):r!=="list"&&r!=="tagName"&&r!=="form"&&!i&&r in t?t[r]=n??"":typeof n!="function"&&r!=="dangerouslySetInnerHTML"&&(r!==(r=r.replace(/^xlink:?/,""))?n==null||n===!1?t.removeAttributeNS("http://www.w3.org/1999/xlink",r.toLowerCase()):t.setAttributeNS("http://www.w3.org/1999/xlink",r.toLowerCase(),n):n==null||n===!1?t.removeAttribute(r):t.setAttribute(r,n))}function me(t){return this.t[t.type](x.event?x.event(t):t)}function he(t,r,n,e,i,a,o,s,c,h){var u,l,d,f,y,m,p,_,g,I,N=r.type;if(r.constructor!==void 0)return null;(u=x.__b)&&u(r);try{e:if(typeof N=="function"){if(_=r.props,g=(u=N.contextType)&&e[u.__c],I=u?g?g.props.value:u.__p:e,n.__c?p=(l=r.__c=n.__c).__p=l.__E:("prototype"in N&&N.prototype.render?r.__c=l=new N(_,I):(r.__c=l=new W(_,I),l.constructor=N,l.render=Je),g&&g.sub(l),l.props=_,l.state||(l.state={}),l.context=I,l.__n=e,d=l.__d=!0,l.__h=[]),l.__s==null&&(l.__s=l.state),N.getDerivedStateFromProps!=null&&R(l.__s==l.state?l.__s=R({},l.__s):l.__s,N.getDerivedStateFromProps(_,l.__s)),d)N.getDerivedStateFromProps==null&&l.componentWillMount!=null&&l.componentWillMount(),l.componentDidMount!=null&&o.push(l);else{if(N.getDerivedStateFromProps==null&&s==null&&l.componentWillReceiveProps!=null&&l.componentWillReceiveProps(_,I),!s&&l.shouldComponentUpdate!=null&&l.shouldComponentUpdate(_,l.__s,I)===!1){for(l.props=_,l.state=l.__s,l.__d=!1,l.__v=r,r.__e=c!=null?c!==n.__e?c:n.__e:null,r.__k=n.__k,u=0;u<r.__k.length;u++)r.__k[u]&&(r.__k[u].__p=r);break e}l.componentWillUpdate!=null&&l.componentWillUpdate(_,l.__s,I)}for(f=l.props,y=l.state,l.context=I,l.props=_,l.state=l.__s,(u=x.__r)&&u(r),l.__d=!1,l.__v=r,l.__P=t,u=l.render(l.props,l.state,l.context),r.__k=ce(u!=null&&u.type==G&&u.key==null?u.props.children:u),l.getChildContext!=null&&(e=R(R({},e),l.getChildContext())),d||l.getSnapshotBeforeUpdate==null||(m=l.getSnapshotBeforeUpdate(f,y)),Ie(t,r,n,e,i,a,o,c,h),l.base=r.__e;u=l.__h.pop();)l.__s&&(l.state=l.__s),u.call(l);d||f==null||l.componentDidUpdate==null||l.componentDidUpdate(f,y,m),p&&(l.__E=l.__p=null)}else r.__e=Ze(n.__e,r,n,e,i,a,o,h);(u=x.diffed)&&u(r)}catch(Le){x.__e(Le,r,n)}return r.__e}function Ne(t,r){for(var n;n=t.pop();)try{n.componentDidMount()}catch(e){x.__e(e,n.__v)}x.__c&&x.__c(r)}function Ze(t,r,n,e,i,a,o,s){var c,h,u,l,d=n.props,f=r.props;if(i=r.type==="svg"||i,t==null&&a!=null){for(c=0;c<a.length;c++)if((h=a[c])!=null&&(r.type===null?h.nodeType===3:h.localName===r.type)){t=h,a[c]=null;break}}if(t==null){if(r.type===null)return document.createTextNode(f);t=i?document.createElementNS("http://www.w3.org/2000/svg",r.type):document.createElement(r.type),a=null}return r.type===null?d!==f&&(a!=null&&(a[a.indexOf(t)]=null),t.data=f):r!==n&&(a!=null&&(a=le.slice.call(t.childNodes)),u=(d=n.props||j).dangerouslySetInnerHTML,l=f.dangerouslySetInnerHTML,s||(l||u)&&(l&&u&&l.__html==u.__html||(t.innerHTML=l&&l.__html||"")),Ye(t,f,d,i,s),r.__k=r.props.children,l||Ie(t,r,n,e,r.type!=="foreignObject"&&i,a,o,j,s),s||("value"in f&&f.value!==void 0&&f.value!==t.value&&(t.value=f.value==null?"":f.value),"checked"in f&&f.checked!==void 0&&f.checked!==t.checked&&(t.checked=f.checked))),t}function Re(t,r,n){try{typeof t=="function"?t(r):t.current=r}catch(e){x.__e(e,n)}}function je(t,r,n){var e,i,a;if(x.unmount&&x.unmount(t),(e=t.ref)&&Re(e,null,r),n||typeof t.type=="function"||(n=(i=t.__e)!=null),t.__e=t.l=null,(e=t.__c)!=null){if(e.componentWillUnmount)try{e.componentWillUnmount()}catch(o){x.__e(o,r)}e.base=e.__P=null}if(e=t.__k)for(a=0;a<e.length;a++)e[a]&&je(e[a],r,n);i!=null&&Se(i)}function Je(t,r,n){return this.constructor(t,n)}function Qe(t,r,n){var e,i,a;x.__p&&x.__p(t,r),i=(e=n===Ee)?null:r.__k,t=b(G,null,[t]),a=[],he(r,r.__k=t,i||j,j,r.ownerSVGElement!==void 0,i?null:le.slice.call(r.childNodes),a,!1,j,e),Ne(a,t)}x={},W.prototype.setState=function(t,r){var n=this.__s!==this.state&&this.__s||(this.__s=R({},this.state));(typeof t!="function"||(t=t(n,this.props)))&&R(n,t),t!=null&&this.__v&&(this.u=!1,r&&this.__h.push(r),te(this))},W.prototype.forceUpdate=function(t){this.__v&&(t&&this.__h.push(t),this.u=!0,te(this))},W.prototype.render=G,U=[],ke=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,ie=x.debounceRendering,x.__e=function(t,r,n){for(var e;r=r.__p;)if((e=r.__c)&&!e.__p)try{if(e.constructor&&e.constructor.getDerivedStateFromError!=null)e.setState(e.constructor.getDerivedStateFromError(t));else{if(e.componentDidCatch==null)continue;e.componentDidCatch(t)}return te(e.__E=e)}catch(i){t=i}throw t},Ee=j;function et(t,r){for(var n=0;n<r.length;n++){var e=r[n];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}function tt(t,r,n){return r&&et(t.prototype,r),t}function S(){return S=Object.assign||function(t){for(var r=arguments,n=1;n<arguments.length;n++){var e=r[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},S.apply(this,arguments)}var rt="[-\\+]?\\d+%?",nt="[-\\+]?\\d*\\.\\d+%?",M="(?:"+nt+")|(?:"+rt+")",Ae="[\\s|\\(]+("+M+")[,|\\s]+("+M+")[,|\\s]+("+M+")\\s*\\)?",Me="[\\s|\\(]+("+M+")[,|\\s]+("+M+")[,|\\s]+("+M+")[,|\\s]+("+M+")\\s*\\)?",it=new RegExp("rgb"+Ae),at=new RegExp("rgba"+Me),ot=new RegExp("hsl"+Ae),st=new RegExp("hsla"+Me),V="^(?:#?|0x?)",P="([0-9a-fA-F]{1})",T="([0-9a-fA-F]{2})",lt=new RegExp(V+P+P+P+"$"),ct=new RegExp(V+P+P+P+P+"$"),ht=new RegExp(V+T+T+T+"$"),ut=new RegExp(V+T+T+T+T+"$"),dt=2e3,ft=4e4,B=Math.log,O=Math.round,L=Math.floor;function C(t,r,n){return Math.min(Math.max(t,r),n)}function k(t,r){var n=t.indexOf("%")>-1,e=parseFloat(t);return n?r/100*e:e}function E(t){return parseInt(t,16)}function A(t){return t.toString(16).padStart(2,"0")}var $=(function(){function t(n,e){this.$={h:0,s:0,v:0,a:1},n&&this.set(n),this.onChange=e,this.initialValue=S({},this.$)}var r=t.prototype;return r.set=function(e){if(typeof e=="string")/^(?:#?|0x?)[0-9a-fA-F]{3,8}$/.test(e)?this.hexString=e:/^rgba?/.test(e)?this.rgbString=e:/^hsla?/.test(e)&&(this.hslString=e);else if(typeof e=="object")e instanceof t?this.hsva=e.hsva:"r"in e&&"g"in e&&"b"in e?this.rgb=e:"h"in e&&"s"in e&&"v"in e?this.hsv=e:"h"in e&&"s"in e&&"l"in e?this.hsl=e:"kelvin"in e&&(this.kelvin=e.kelvin);else throw new Error("Invalid color value")},r.setChannel=function(e,i,a){var o;this[e]=S({},this[e],(o={},o[i]=a,o))},r.reset=function(){this.hsva=this.initialValue},r.clone=function(){return new t(this)},r.unbind=function(){this.onChange=void 0},t.hsvToRgb=function(e){var i=e.h/60,a=e.s/100,o=e.v/100,s=L(i),c=i-s,h=o*(1-a),u=o*(1-c*a),l=o*(1-(1-c)*a),d=s%6,f=[o,u,h,h,l,o][d],y=[l,o,o,u,h,h][d],m=[h,h,l,o,o,u][d];return{r:C(f*255,0,255),g:C(y*255,0,255),b:C(m*255,0,255)}},t.rgbToHsv=function(e){var i=e.r/255,a=e.g/255,o=e.b/255,s=Math.max(i,a,o),c=Math.min(i,a,o),h=s-c,u=0,l=s,d=s===0?0:h/s;switch(s){case c:u=0;break;case i:u=(a-o)/h+(a<o?6:0);break;case a:u=(o-i)/h+2;break;case o:u=(i-a)/h+4;break}return{h:u*60%360,s:C(d*100,0,100),v:C(l*100,0,100)}},t.hsvToHsl=function(e){var i=e.s/100,a=e.v/100,o=(2-i)*a,s=o<=1?o:2-o,c=s<1e-9?0:i*a/s;return{h:e.h,s:C(c*100,0,100),l:C(o*50,0,100)}},t.hslToHsv=function(e){var i=e.l*2,a=e.s*(i<=100?i:200-i)/100,o=i+a<1e-9?0:2*a/(i+a);return{h:e.h,s:C(o*100,0,100),v:C((i+a)/2,0,100)}},t.kelvinToRgb=function(e){var i=e/100,a,o,s;return i<66?(a=255,o=-155.25485562709179-.44596950469579133*(o=i-2)+104.49216199393888*B(o),s=i<20?0:-254.76935184120902+.8274096064007395*(s=i-10)+115.67994401066147*B(s)):(a=351.97690566805693+.114206453784165*(a=i-55)-40.25366309332127*B(a),o=325.4494125711974+.07943456536662342*(o=i-50)-28.0852963507957*B(o),s=255),{r:C(L(a),0,255),g:C(L(o),0,255),b:C(L(s),0,255)}},t.rgbToKelvin=function(e){for(var i=e.r,a=e.b,o=.4,s=dt,c=ft,h;c-s>o;){h=(c+s)*.5;var u=t.kelvinToRgb(h);u.b/u.r>=a/i?c=h:s=h}return h},tt(t,[{key:"hsv",get:function(){var e=this.$;return{h:e.h,s:e.s,v:e.v}},set:function(e){var i=this.$;if(e=S({},i,e),this.onChange){var a={h:!1,v:!1,s:!1,a:!1};for(var o in i)a[o]=e[o]!=i[o];this.$=e,(a.h||a.s||a.v||a.a)&&this.onChange(this,a)}else this.$=e}},{key:"hsva",get:function(){return S({},this.$)},set:function(e){this.hsv=e}},{key:"hue",get:function(){return this.$.h},set:function(e){this.hsv={h:e}}},{key:"saturation",get:function(){return this.$.s},set:function(e){this.hsv={s:e}}},{key:"value",get:function(){return this.$.v},set:function(e){this.hsv={v:e}}},{key:"alpha",get:function(){return this.$.a},set:function(e){this.hsv=S({},this.hsv,{a:e})}},{key:"kelvin",get:function(){return t.rgbToKelvin(this.rgb)},set:function(e){this.rgb=t.kelvinToRgb(e)}},{key:"red",get:function(){var e=this.rgb;return e.r},set:function(e){this.rgb=S({},this.rgb,{r:e})}},{key:"green",get:function(){var e=this.rgb;return e.g},set:function(e){this.rgb=S({},this.rgb,{g:e})}},{key:"blue",get:function(){var e=this.rgb;return e.b},set:function(e){this.rgb=S({},this.rgb,{b:e})}},{key:"rgb",get:function(){var e=t.hsvToRgb(this.$),i=e.r,a=e.g,o=e.b;return{r:O(i),g:O(a),b:O(o)}},set:function(e){this.hsv=S({},t.rgbToHsv(e),{a:e.a===void 0?1:e.a})}},{key:"rgba",get:function(){return S({},this.rgb,{a:this.alpha})},set:function(e){this.rgb=e}},{key:"hsl",get:function(){var e=t.hsvToHsl(this.$),i=e.h,a=e.s,o=e.l;return{h:O(i),s:O(a),l:O(o)}},set:function(e){this.hsv=S({},t.hslToHsv(e),{a:e.a===void 0?1:e.a})}},{key:"hsla",get:function(){return S({},this.hsl,{a:this.alpha})},set:function(e){this.hsl=e}},{key:"rgbString",get:function(){var e=this.rgb;return"rgb("+e.r+", "+e.g+", "+e.b+")"},set:function(e){var i,a,o,s,c=1;if((i=it.exec(e))?(a=k(i[1],255),o=k(i[2],255),s=k(i[3],255)):(i=at.exec(e))&&(a=k(i[1],255),o=k(i[2],255),s=k(i[3],255),c=k(i[4],1)),i)this.rgb={r:a,g:o,b:s,a:c};else throw new Error("Invalid rgb string")}},{key:"rgbaString",get:function(){var e=this.rgba;return"rgba("+e.r+", "+e.g+", "+e.b+", "+e.a+")"},set:function(e){this.rgbString=e}},{key:"hexString",get:function(){var e=this.rgb;return"#"+A(e.r)+A(e.g)+A(e.b)},set:function(e){var i,a,o,s,c=255;if((i=lt.exec(e))?(a=E(i[1])*17,o=E(i[2])*17,s=E(i[3])*17):(i=ct.exec(e))?(a=E(i[1])*17,o=E(i[2])*17,s=E(i[3])*17,c=E(i[4])*17):(i=ht.exec(e))?(a=E(i[1]),o=E(i[2]),s=E(i[3])):(i=ut.exec(e))&&(a=E(i[1]),o=E(i[2]),s=E(i[3]),c=E(i[4])),i)this.rgb={r:a,g:o,b:s,a:c/255};else throw new Error("Invalid hex string")}},{key:"hex8String",get:function(){var e=this.rgba;return"#"+A(e.r)+A(e.g)+A(e.b)+A(L(e.a*255))},set:function(e){this.hexString=e}},{key:"hslString",get:function(){var e=this.hsl;return"hsl("+e.h+", "+e.s+"%, "+e.l+"%)"},set:function(e){var i,a,o,s,c=1;if((i=ot.exec(e))?(a=k(i[1],360),o=k(i[2],100),s=k(i[3],100)):(i=st.exec(e))&&(a=k(i[1],360),o=k(i[2],100),s=k(i[3],100),c=k(i[4],1)),i)this.hsl={h:a,s:o,l:s,a:c};else throw new Error("Invalid hsl string")}},{key:"hslaString",get:function(){var e=this.hsla;return"hsla("+e.h+", "+e.s+"%, "+e.l+"%, "+e.a+")"},set:function(e){this.hslString=e}}]),t})(),vt={sliderShape:"bar",sliderType:"value",minTemperature:2200,maxTemperature:11e3};function ue(t){var r,n=t.width,e=t.sliderSize,i=t.borderWidth,a=t.handleRadius,o=t.padding,s=t.sliderShape,c=t.layoutDirection==="horizontal";return e=(r=e)!=null?r:o*2+a*2,s==="circle"?{handleStart:t.padding+t.handleRadius,handleRange:n-o*2-a*2,width:n,height:n,cx:n/2,cy:n/2,radius:n/2-i/2}:{handleStart:e/2,handleRange:n-e,radius:e/2,x:0,y:0,width:c?e:n,height:c?n:e}}function gt(t,r){var n=r.hsva,e=r.rgb;switch(t.sliderType){case"red":return e.r/2.55;case"green":return e.g/2.55;case"blue":return e.b/2.55;case"alpha":return n.a*100;case"kelvin":var i=t.minTemperature,a=t.maxTemperature,o=a-i,s=(r.kelvin-i)/o*100;return Math.max(0,Math.min(s,100));case"hue":return n.h/=3.6;case"saturation":return n.s;case"value":default:return n.v}}function _t(t,r,n){var e=ue(t),i=e.handleRange,a=e.handleStart,o;t.layoutDirection==="horizontal"?o=-1*n+i+a:o=r-a,o=Math.max(Math.min(o,i),0);var s=Math.round(100/i*o);switch(t.sliderType){case"kelvin":var c=t.minTemperature,h=t.maxTemperature,u=h-c;return c+u*(s/100);case"alpha":return s/100;case"hue":return s*3.6;case"red":case"blue":case"green":return s*2.55;default:return s}}function mt(t,r){var n=ue(t),e=n.width,i=n.height,a=n.handleRange,o=n.handleStart,s=t.layoutDirection==="horizontal",c=gt(t,r),h=s?e/2:i/2,u=o+c/100*a;return s&&(u=-1*u+a+o*2),{x:s?h:u,y:s?u:h}}function bt(t,r){var n=r.hsv,e=r.rgb;switch(t.sliderType){case"red":return[[0,"rgb(0,"+e.g+","+e.b+")"],[100,"rgb(255,"+e.g+","+e.b+")"]];case"green":return[[0,"rgb("+e.r+",0,"+e.b+")"],[100,"rgb("+e.r+",255,"+e.b+")"]];case"blue":return[[0,"rgb("+e.r+","+e.g+",0)"],[100,"rgb("+e.r+","+e.g+",255)"]];case"alpha":return[[0,"rgba("+e.r+","+e.g+","+e.b+",0)"],[100,"rgb("+e.r+","+e.g+","+e.b+")"]];case"kelvin":for(var i=[],a=t.minTemperature,o=t.maxTemperature,s=8,c=o-a,h=a,u=0;h<o;h+=c/s,u+=1){var l=$.kelvinToRgb(h),d=l.r,f=l.g,y=l.b;i.push([100/s*u,"rgb("+d+","+f+","+y+")"])}return i;case"hue":return[[0,"#f00"],[16.666,"#ff0"],[33.333,"#0f0"],[50,"#0ff"],[66.666,"#00f"],[83.333,"#f0f"],[100,"#f00"]];case"saturation":var m=$.hsvToHsl({h:n.h,s:0,v:n.v}),p=$.hsvToHsl({h:n.h,s:100,v:n.v});return[[0,"hsl("+m.h+","+m.s+"%,"+m.l+"%)"],[100,"hsl("+p.h+","+p.s+"%,"+p.l+"%)"]];case"value":default:var _=$.hsvToHsl({h:n.h,s:n.s,v:100});return[[0,"#000"],[100,"hsl("+_.h+","+_.s+"%,"+_.l+"%)"]]}}var Pe=Math.PI*2,yt=function(r,n){return(r%n+n)%n},Te=function(r,n){return Math.sqrt(r*r+n*n)};function He(t){return t.width/2-t.padding-t.handleRadius-t.borderWidth}function xt(t,r,n){var e=q(t),i=e.cx,a=e.cy,o=t.width/2;return Te(i-r,a-n)<o}function q(t){var r=t.width/2;return{width:t.width,radius:r-t.borderWidth,cx:r,cy:r}}function De(t,r,n){var e=t.wheelAngle,i=t.wheelDirection;return n&&i==="clockwise"?r=e+r:i==="clockwise"?r=360-e+r:n&&i==="anticlockwise"?r=e+180-r:i==="anticlockwise"&&(r=e-r),yt(r,360)}function pt(t,r){var n=r.hsv,e=q(t),i=e.cx,a=e.cy,o=He(t),s=(180+De(t,n.h,!0))*(Pe/360),c=n.s/100*o,h=t.wheelDirection==="clockwise"?-1:1;return{x:i+c*Math.cos(s)*h,y:a+c*Math.sin(s)*h}}function be(t,r,n){var e=q(t),i=e.cx,a=e.cy,o=He(t);r=i-r,n=a-n;var s=De(t,Math.atan2(-n,-r)*(360/Pe)),c=Math.min(Te(r,n),o);return{h:Math.round(s),s:Math.round(100/o*c)}}function de(t){var r=t.width,n=t.boxHeight,e=t.padding,i=t.handleRadius;return{width:r,height:n??r,radius:e+i}}function ye(t,r,n){var e=de(t),i=e.width,a=e.height,o=e.radius,s=o,c=i-o*2,h=a-o*2,u=(r-s)/c*100,l=(n-s)/h*100;return{s:Math.max(0,Math.min(u,100)),v:Math.max(0,Math.min(100-l,100))}}function wt(t,r){var n=de(t),e=n.width,i=n.height,a=n.radius,o=r.hsv,s=a,c=e-a*2,h=i-a*2;return{x:s+o.s/100*c,y:s+(h-o.v/100*h)}}function kt(t,r){var n=r.hue;return[[[0,"#fff"],[100,"hsl("+n+",100%,50%)"]],[[0,"rgba(0,0,0,0)"],[100,"#000"]]]}var re;function Et(t){re||(re=document.getElementsByTagName("base"));var r=window.navigator.userAgent,n=/^((?!chrome|android).)*safari/i.test(r),e=/iPhone|iPod|iPad/i.test(r),i=window.location;return(n||e)&&re.length>0?i.protocol+"//"+i.host+i.pathname+i.search+t:t}function Oe(t,r,n,e){for(var i=0;i<e.length;i++){var a=e[i].x-r,o=e[i].y-n,s=Math.sqrt(a*a+o*o);if(s<t.handleRadius)return i}return null}function fe(t){return{boxSizing:"border-box",border:t.borderWidth+"px solid "+t.borderColor}}function oe(t,r,n){return t+"-gradient("+r+", "+n.map(function(e){var i=e[0],a=e[1];return a+" "+i+"%"}).join(",")+")"}function w(t){return typeof t=="string"?t:t+"px"}var St={width:300,height:300,color:"#fff",colors:[],padding:6,layoutDirection:"vertical",borderColor:"#fff",borderWidth:0,handleRadius:8,activeHandleRadius:null,handleSvg:null,handleProps:{x:0,y:0},wheelLightness:!0,wheelAngle:0,wheelDirection:"anticlockwise",sliderSize:null,sliderMargin:12,boxHeight:null},xe=["mousemove","touchmove","mouseup","touchend"],K=(function(t){function r(n){t.call(this,n),this.uid=(Math.random()+1).toString(36).substring(5)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.render=function(e){var i=this.handleEvent.bind(this),a={onMouseDown:i,ontouchstart:i},o=e.layoutDirection==="horizontal",s=e.margin===null?e.sliderMargin:e.margin,c={overflow:"visible",display:o?"inline-block":"block"};return e.index>0&&(c[o?"marginLeft":"marginTop"]=s),b(G,null,e.children(this.uid,a,c))},r.prototype.handleEvent=function(e){var i=this,a=this.props.onInput,o=this.base.getBoundingClientRect();e.preventDefault();var s=e.touches?e.changedTouches[0]:e,c=s.clientX-o.left,h=s.clientY-o.top;switch(e.type){case"mousedown":case"touchstart":var u=a(c,h,0);u!==!1&&xe.forEach(function(l){document.addEventListener(l,i,{passive:!1})});break;case"mousemove":case"touchmove":a(c,h,1);break;case"mouseup":case"touchend":a(c,h,2),xe.forEach(function(l){document.removeEventListener(l,i,{passive:!1})});break}},r})(W);function H(t){var r=t.r,n=t.url,e=r,i=r;return b("svg",{className:"IroHandle IroHandle--"+t.index+" "+(t.isActive?"IroHandle--isActive":""),style:{"-webkit-tap-highlight-color":"rgba(0, 0, 0, 0);",transform:"translate("+w(t.x)+", "+w(t.y)+")",willChange:"transform",top:w(-r),left:w(-r),width:w(r*2),height:w(r*2),position:"absolute",overflow:"visible"}},n&&b("use",Object.assign({xlinkHref:Et(n)},t.props)),!n&&b("circle",{cx:e,cy:i,r,fill:"none","stroke-width":2,stroke:"#000"}),!n&&b("circle",{cx:e,cy:i,r:r-2,fill:t.fill,"stroke-width":2,stroke:"#fff"}))}H.defaultProps={fill:"none",x:0,y:0,r:8,url:null,props:{x:0,y:0}};function F(t){var r=t.activeIndex,n=r!==void 0&&r<t.colors.length?t.colors[r]:t.color,e=ue(t),i=e.width,a=e.height,o=e.radius,s=mt(t,n),c=bt(t,n);function h(u,l,d){var f=_t(t,u,l);t.parent.inputActive=!0,n[t.sliderType]=f,t.onInput(d,t.id)}return b(K,Object.assign({},t,{onInput:h}),function(u,l,d){return b("div",Object.assign({},l,{className:"IroSlider",style:Object.assign({},{position:"relative",width:w(i),height:w(a),borderRadius:w(o),background:"conic-gradient(#ccc 25%, #fff 0 50%, #ccc 0 75%, #fff 0)",backgroundSize:"8px 8px"},d)}),b("div",{className:"IroSliderGradient",style:Object.assign({},{position:"absolute",top:0,left:0,width:"100%",height:"100%",borderRadius:w(o),background:oe("linear",t.layoutDirection==="horizontal"?"to top":"to right",c)},fe(t))}),b(H,{isActive:!0,index:n.index,r:t.handleRadius,url:t.handleSvg,props:t.handleProps,x:s.x,y:s.y}))})}F.defaultProps=Object.assign({},vt);function Ct(t){var r=de(t),n=r.width,e=r.height,i=r.radius,a=t.colors,o=t.parent,s=t.activeIndex,c=s!==void 0&&s<t.colors.length?t.colors[s]:t.color,h=kt(t,c),u=a.map(function(d){return wt(t,d)});function l(d,f,y){if(y===0){var m=Oe(t,d,f,u);m!==null?o.setActiveColor(m):(o.inputActive=!0,c.hsv=ye(t,d,f),t.onInput(y,t.id))}else y===1&&(o.inputActive=!0,c.hsv=ye(t,d,f));t.onInput(y,t.id)}return b(K,Object.assign({},t,{onInput:l}),function(d,f,y){return b("div",Object.assign({},f,{className:"IroBox",style:Object.assign({},{width:w(n),height:w(e),position:"relative"},y)}),b("div",{className:"IroBox",style:Object.assign({},{width:"100%",height:"100%",borderRadius:w(i)},fe(t),{background:oe("linear","to bottom",h[1])+","+oe("linear","to right",h[0])})}),a.filter(function(m){return m!==c}).map(function(m){return b(H,{isActive:!1,index:m.index,fill:m.hslString,r:t.handleRadius,url:t.handleSvg,props:t.handleProps,x:u[m.index].x,y:u[m.index].y})}),b(H,{isActive:!0,index:c.index,fill:c.hslString,r:t.activeHandleRadius||t.handleRadius,url:t.handleSvg,props:t.handleProps,x:u[c.index].x,y:u[c.index].y}))})}var It="conic-gradient(red, yellow, lime, aqua, blue, magenta, red)",Nt="conic-gradient(red, magenta, blue, aqua, lime, yellow, red)";function $e(t){var r=q(t),n=r.width,e=t.colors;t.borderWidth;var i=t.parent,a=t.color,o=a.hsv,s=e.map(function(u){return pt(t,u)}),c={position:"absolute",top:0,left:0,width:"100%",height:"100%",borderRadius:"50%",boxSizing:"border-box"};function h(u,l,d){if(d===0){if(!xt(t,u,l))return!1;var f=Oe(t,u,l,s);f!==null?i.setActiveColor(f):(i.inputActive=!0,a.hsv=be(t,u,l),t.onInput(d,t.id))}else d===1&&(i.inputActive=!0,a.hsv=be(t,u,l));t.onInput(d,t.id)}return b(K,Object.assign({},t,{onInput:h}),function(u,l,d){return b("div",Object.assign({},l,{className:"IroWheel",style:Object.assign({},{width:w(n),height:w(n),position:"relative"},d)}),b("div",{className:"IroWheelHue",style:Object.assign({},c,{transform:"rotateZ("+(t.wheelAngle+90)+"deg)",background:t.wheelDirection==="clockwise"?It:Nt})}),b("div",{className:"IroWheelSaturation",style:Object.assign({},c,{background:"radial-gradient(circle closest-side, #fff, transparent)"})}),t.wheelLightness&&b("div",{className:"IroWheelLightness",style:Object.assign({},c,{background:"#000",opacity:1-o.v/100})}),b("div",{className:"IroWheelBorder",style:Object.assign({},c,fe(t))}),e.filter(function(f){return f!==a}).map(function(f){return b(H,{isActive:!1,index:f.index,fill:f.hslString,r:t.handleRadius,url:t.handleSvg,props:t.handleProps,x:s[f.index].x,y:s[f.index].y})}),b(H,{isActive:!0,index:a.index,fill:a.hslString,r:t.activeHandleRadius||t.handleRadius,url:t.handleSvg,props:t.handleProps,x:s[a.index].x,y:s[a.index].y}))})}function Rt(t){var r=function(n,e){var i,a=document.createElement("div");Qe(b(t,Object.assign({},{ref:function(s){return i=s}},e)),a);function o(){var s=n instanceof Element?n:document.querySelector(n);s.appendChild(i.base),i.onMount(s)}return document.readyState!=="loading"?o():document.addEventListener("DOMContentLoaded",o),i};return r.prototype=t.prototype,Object.assign(r,t),r.__component=t,r}var We=(function(t){function r(n){var e=this;t.call(this,n),this.colors=[],this.inputActive=!1,this.events={},this.activeEvents={},this.deferredEvents={},this.id=n.id;var i=n.colors.length>0?n.colors:[n.color];i.forEach(function(a){return e.addColor(a)}),this.setActiveColor(0),this.state=Object.assign({},n,{color:this.color,colors:this.colors,layout:n.layout})}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.addColor=function(e,i){i===void 0&&(i=this.colors.length);var a=new $(e,this.onColorChange.bind(this));this.colors.splice(i,0,a),this.colors.forEach(function(o,s){return o.index=s}),this.state&&this.setState({colors:this.colors}),this.deferredEmit("color:init",a)},r.prototype.removeColor=function(e){var i=this.colors.splice(e,1)[0];i.unbind(),this.colors.forEach(function(a,o){return a.index=o}),this.state&&this.setState({colors:this.colors}),i.index===this.color.index&&this.setActiveColor(0),this.emit("color:remove",i)},r.prototype.setActiveColor=function(e){this.color=this.colors[e],this.state&&this.setState({color:this.color}),this.emit("color:setActive",this.color)},r.prototype.setColors=function(e,i){var a=this;i===void 0&&(i=0),this.colors.forEach(function(o){return o.unbind()}),this.colors=[],e.forEach(function(o){return a.addColor(o)}),this.setActiveColor(i),this.emit("color:setAll",this.colors)},r.prototype.on=function(e,i){var a=this,o=this.events;(Array.isArray(e)?e:[e]).forEach(function(s){(o[s]||(o[s]=[])).push(i),a.deferredEvents[s]&&(a.deferredEvents[s].forEach(function(c){i.apply(null,c)}),a.deferredEvents[s]=[])})},r.prototype.off=function(e,i){var a=this;(Array.isArray(e)?e:[e]).forEach(function(o){var s=a.events[o];s&&s.splice(s.indexOf(i),1)})},r.prototype.emit=function(e){for(var i=this,a=[],o=arguments.length-1;o-- >0;)a[o]=arguments[o+1];var s=this.activeEvents,c=s.hasOwnProperty(e)?s[e]:!1;if(!c){s[e]=!0;var h=this.events[e]||[];h.forEach(function(u){return u.apply(i,a)}),s[e]=!1}},r.prototype.deferredEmit=function(e){for(var i,a=[],o=arguments.length-1;o-- >0;)a[o]=arguments[o+1];var s=this.deferredEvents;(i=this).emit.apply(i,[e].concat(a)),(s[e]||(s[e]=[])).push(a)},r.prototype.setOptions=function(e){this.setState(e)},r.prototype.resize=function(e){this.setOptions({width:e})},r.prototype.reset=function(){this.colors.forEach(function(e){return e.reset()}),this.setState({colors:this.colors})},r.prototype.onMount=function(e){this.el=e,this.deferredEmit("mount",this)},r.prototype.onColorChange=function(e,i){this.setState({color:this.color}),this.inputActive&&(this.inputActive=!1,this.emit("input:change",e,i)),this.emit("color:change",e,i)},r.prototype.emitInputEvent=function(e,i){e===0?this.emit("input:start",this.color,i):e===1?this.emit("input:move",this.color,i):e===2&&this.emit("input:end",this.color,i)},r.prototype.render=function(e,i){var a=this,o=i.layout;return Array.isArray(o)||(o=[{component:$e},{component:F}],i.transparency&&o.push({component:F,options:{sliderType:"alpha"}})),b("div",{class:"IroColorPicker",id:i.id,style:{display:i.display}},o.map(function(s,c){var h=s.component,u=s.options;return b(h,Object.assign({},i,u,{ref:void 0,onInput:a.emitInputEvent.bind(a),parent:a,index:c}))}))},r})(W);We.defaultProps=Object.assign({},St,{colors:[],display:"block",id:null,layout:"default",margin:null});var jt=Rt(We),se;(function(t){t.version="5.5.2",t.Color=$,t.ColorPicker=jt,(function(r){r.h=b,r.ComponentBase=K,r.Handle=H,r.Slider=F,r.Wheel=$e,r.Box=Ct})(t.ui||(t.ui={}))})(se||(se={}));var ne=se;function At({selectedText:t,existingComment:r,onSave:n,onCancel:e}){const i=Be(),[a,o]=D.useState(r?.text||""),[s,c]=D.useState(r?.highlightColor?.border||"#FFEB3B"),[h,u]=D.useState(null),l=D.useRef(null);D.useEffect(()=>{if(l.current&&!h){const _=new ne.ColorPicker(l.current,{width:200,color:s,borderWidth:2,borderColor:"#fff",layout:[{component:ne.ui.Wheel,options:{}},{component:ne.ui.Slider,options:{sliderType:"value"}}]});_.on("color:change",g=>{c(g.hexString.toUpperCase())}),u(_)}return()=>{h&&h.off("color:change")}},[h]),D.useEffect(()=>{const _=g=>{(g.ctrlKey||g.metaKey)&&g.key==="Enter"?(g.preventDefault(),d()):g.key==="Escape"&&(g.preventDefault(),e())};return document.addEventListener("keydown",_),()=>document.removeEventListener("keydown",_)},[a,s,e]);const d=()=>{a.trim()&&y(s)&&n(a.trim(),s)},f=_=>{const g=_.target.value.toUpperCase();y(g)?(c(g),h&&(h.color.hexString=g)):c(g)},y=_=>/^#[0-9A-Fa-f]{6}$/.test(_),m=()=>{if(y(s)){const _=parseInt(s.slice(1,3),16),g=parseInt(s.slice(3,5),16),I=parseInt(s.slice(5,7),16);return{backgroundColor:`rgba(${_}, ${g}, ${I}, 0.3)`,borderColor:s,borderWidth:"2px"}}return{}},p=a.trim().length>0&&y(s);return v.jsxs("div",{className:Ue("h-full flex flex-col overflow-hidden p-6",`vscode-${i}`),children:[v.jsxs("div",{className:"flex items-center justify-between mb-4 flex-shrink-0",children:[v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(Fe,{className:"h-5 w-5 text-primary"}),v.jsx("h1",{className:"text-lg font-semibold",children:r?"Edit Comment":"Add Comment"})]}),v.jsx(Y,{variant:"ghost",size:"sm",onClick:e,className:"h-8 w-8 p-0",children:v.jsx(ve,{className:"h-4 w-4"})})]}),v.jsxs("div",{className:"flex-1 overflow-y-auto space-y-4 min-h-0",children:[v.jsxs(Z,{children:[v.jsx(J,{className:"pb-3",children:v.jsx(Q,{className:"text-sm",children:"Selected Text"})}),v.jsx(ee,{children:v.jsx("div",{className:"p-3 rounded-md bg-muted font-mono text-sm leading-relaxed max-h-32 overflow-y-auto border",style:m(),children:t})})]}),v.jsxs(Z,{children:[v.jsx(J,{className:"pb-3",children:v.jsxs(Q,{className:"text-sm flex items-center gap-2",children:[v.jsx(Xe,{className:"h-4 w-4"}),"Choose Highlight Color"]})}),v.jsxs(ee,{className:"space-y-4",children:[v.jsx("div",{className:"flex justify-center p-4 bg-muted/30 rounded-lg border",children:v.jsx("div",{ref:l})}),v.jsxs("div",{className:"flex items-center gap-3",children:[v.jsxs("div",{className:"flex-1",children:[v.jsx("label",{className:"text-xs font-medium text-muted-foreground mb-1 block",children:"Hex Color Code"}),v.jsx("input",{type:"text",value:s,onChange:f,className:"w-full px-3 py-2 text-sm font-mono bg-background border rounded-md focus:outline-none focus:ring-2 focus:ring-ring",placeholder:"#FFEB3B",maxLength:7})]}),v.jsxs("div",{className:"flex flex-col items-center gap-1",children:[v.jsx("div",{className:"w-12 h-12 border-2 rounded-md shadow-sm",style:{backgroundColor:y(s)?s:"#gray",borderColor:y(s)?s:"#gray"}}),v.jsx("span",{className:"text-xs text-muted-foreground",children:"Preview"})]})]})]})]}),v.jsxs(Z,{children:[v.jsx(J,{className:"pb-3",children:v.jsx(Q,{className:"text-sm",children:"Your Comment"})}),v.jsxs(ee,{children:[v.jsx("textarea",{value:a,onChange:_=>o(_.target.value),className:"w-full min-h-24 px-3 py-2 text-sm bg-background border rounded-md resize-y focus:outline-none focus:ring-2 focus:ring-ring",placeholder:"Enter your comment here...",autoFocus:!0}),v.jsx("div",{className:"mt-2 text-xs text-muted-foreground",children:"Tip: Use Ctrl/Cmd + Enter to save quickly"})]})]})]}),v.jsxs("div",{className:"flex justify-end gap-2 pt-4 mt-4 border-t border-border flex-shrink-0",children:[v.jsxs(Y,{variant:"outline",onClick:e,className:"flex items-center gap-2",children:[v.jsx(ve,{className:"h-4 w-4"}),"Cancel"]}),v.jsxs(Y,{onClick:d,disabled:!p,className:"flex items-center gap-2",children:[v.jsx(Ge,{className:"h-4 w-4"}),r?"Update Comment":"Add Comment"]})]})]})}const pe=window.acquireVsCodeApi?.();function Mt(){const t=window.initialState?.selectedText||"No text selected",r=window.initialState?.existingComment||null,n=(i,a)=>{const o={command:"save",comment:i,color:a};pe?.postMessage(o)},e=()=>{const i={command:"cancel"};pe?.postMessage(i)};return v.jsx(At,{selectedText:t,existingComment:r,onSave:n,onCancel:e})}const we=document.getElementById("root");we&&ze.createRoot(we).render(v.jsx(Mt,{}));
