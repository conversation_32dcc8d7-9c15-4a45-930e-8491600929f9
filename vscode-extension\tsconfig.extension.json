{"compilerOptions": {"module": "Node16", "target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "Node16", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "sourceMap": true, "baseUrl": "."}, "include": ["src/extension", "src/extension.ts", "esbuild.js"], "exclude": ["node_modules"]}