:root {
  --bg: #f8fafc;
  --panel: #ffffff;
  --text: #0f172a;
  --muted: #475569;
  --border: #e2e8f0;
  --brand: #4f46e5;
}

.dark:root {
  --bg: #0f172a;
  --panel: #111827;
  --text: #e5e7eb;
  --muted: #94a3b8;
  --border: #1f2937;
  --brand: #6366f1;
}

html, body, #root { height: 100%; }
body { margin: 0; background: var(--bg); color: var(--text); font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji"; }

.app-container { display: flex; flex-direction: column; height: 100%; }
.app-header { display: flex; align-items: center; justify-content: space-between; padding: 12px 16px; border-bottom: 1px solid var(--border); background: var(--panel); position: sticky; top: 0; z-index: 10; }
.app-header .left { display: flex; align-items: center; gap: 16px; }
.app-title { font-weight: 700; }
.nav a { color: var(--muted); text-decoration: none; margin-right: 12px; padding: 6px 8px; border-radius: 8px; }
.nav a.active { background: color-mix(in srgb, var(--brand) 15%, transparent); color: var(--text); }
.right { display: flex; align-items: center; gap: 10px; }
.status-dot { width: 10px; height: 10px; border-radius: 50%; display: inline-block; }
.status-dot.ok { background: #22c55e; }
.status-dot.err { background: #ef4444; }
.theme-btn { background: var(--panel); border: 1px solid var(--border); color: var(--muted); border-radius: 8px; padding: 6px 10px; cursor: pointer; }
.app-main { padding: 16px; max-width: 1200px; width: 100%; margin: 0 auto; }

.panel { background: var(--panel); border: 1px solid var(--border); border-radius: 12px; padding: 16px; }
.grid { display: grid; gap: 16px; }
.grid.cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.stat { display: flex; flex-direction: column; gap: 4px; }
.stat .label { color: var(--muted); font-size: 12px; }
.stat .value { font-size: 24px; font-weight: 700; }

.select { padding: 8px 10px; background: var(--panel); color: var(--text); border: 1px solid var(--border); border-radius: 8px; }

.progress-bar { width: 100%; height: 8px; background: var(--border); border-radius: 999px; overflow: hidden; }
.progress-bar > span { display: block; height: 100%; background: linear-gradient(90deg, #60a5fa, #a78bfa); }

.task { border: 1px solid var(--border); border-radius: 10px; padding: 10px; }
.task.header { background: color-mix(in srgb, var(--brand) 10%, var(--panel)); }
.task.done { background: color-mix(in srgb, #22c55e 10%, var(--panel)); }
.task.progress { background: color-mix(in srgb, #3b82f6 10%, var(--panel)); }

.muted { color: var(--muted); }

/* Markdown styles (GitHub-like) */
.markdown-content { line-height: 1.7; font-size: 16px; }
.markdown-content pre { padding: 12px; border: 1px solid var(--border); border-radius: 8px; overflow: auto; background: color-mix(in srgb, var(--panel) 90%, #000); }
.markdown-content code { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; }
.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4 { margin: 16px 0 8px; }
.markdown-content table { border-collapse: collapse; width: 100%; }
.markdown-content table th, .markdown-content table td { border: 1px solid var(--border); padding: 6px 10px; }
.markdown-content .mermaid { text-align: center; margin: 20px 0; }

/* Syntax highlighting for markdown source view */
.hljs {
  background: transparent !important;
  color: var(--text);
}

.hljs-comment,
.hljs-quote {
  color: #6a737d;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-addition {
  color: #d73a49;
}

.hljs-number,
.hljs-string,
.hljs-meta .hljs-meta-string,
.hljs-literal,
.hljs-doctag,
.hljs-regexp {
  color: #032f62;
}

.hljs-title,
.hljs-section,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
  color: #6f42c1;
}

.hljs-attribute,
.hljs-attr,
.hljs-variable,
.hljs-template-variable,
.hljs-class .hljs-title,
.hljs-type {
  color: #e36209;
}

.hljs-symbol,
.hljs-bullet,
.hljs-subst,
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-link {
  color: #005cc5;
}

/* Dark mode syntax highlighting */
.dark .hljs-comment,
.dark .hljs-quote {
  color: #8b949e;
}

.dark .hljs-keyword,
.dark .hljs-selector-tag,
.dark .hljs-addition {
  color: #ff7b72;
}

.dark .hljs-number,
.dark .hljs-string,
.dark .hljs-meta .hljs-meta-string,
.dark .hljs-literal,
.dark .hljs-doctag,
.dark .hljs-regexp {
  color: #a5d6ff;
}

.dark .hljs-title,
.dark .hljs-section,
.dark .hljs-name,
.dark .hljs-selector-id,
.dark .hljs-selector-class {
  color: #d2a8ff;
}

.dark .hljs-attribute,
.dark .hljs-attr,
.dark .hljs-variable,
.dark .hljs-template-variable,
.dark .hljs-class .hljs-title,
.dark .hljs-type {
  color: #ffa657;
}

.dark .hljs-symbol,
.dark .hljs-bullet,
.dark .hljs-subst,
.dark .hljs-meta,
.dark .hljs-meta .hljs-keyword,
.dark .hljs-selector-attr,
.dark .hljs-selector-pseudo,
.dark .hljs-link {
  color: #79c0ff;
}


