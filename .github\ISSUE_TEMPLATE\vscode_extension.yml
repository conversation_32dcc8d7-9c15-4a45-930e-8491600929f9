name: 🧩 VSCode Extension Issue
description: Report an issue specific to the VSCode extension
title: "[Extension]: "
labels: ["extension", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Report issues specific to the VSCode extension functionality.

  - type: dropdown
    id: issue-type
    attributes:
      label: Issue Type
      description: What type of extension issue is this?
      options:
        - "Extension not activating"
        - "Dashboard not loading in sidebar"
        - "Archive feature issue"
        - "Sound notifications not working"
        - "Approval workflow issue"
        - "Project detection issue"
        - "Other"
    validations:
      required: true

  - type: textarea
    id: issue-description
    attributes:
      label: What's happening?
      description: Describe the issue you're experiencing
      placeholder: Tell us what's wrong...
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: How can we reproduce this issue?
      placeholder: |
        1. Open VSCode with project...
        2. Click on extension icon...
        3. See issue
    validations:
      required: true

  - type: input
    id: extension-version
    attributes:
      label: Extension Version
      description: What version of the extension are you using?
      placeholder: "0.0.2"
    validations:
      required: true

  - type: input
    id: vscode-version
    attributes:
      label: VSCode Version
      description: What version of VSCode are you using? (Help > About)
      placeholder: "1.85.0"
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      options:
        - Windows
        - macOS
        - Linux
        - Other

  - type: textarea
    id: workspace-info
    attributes:
      label: Workspace Information
      description: Does your project have a .spec-workflow folder?
      placeholder: |
        - Project has .spec-workflow folder: Yes/No
        - Multiple workspaces open: Yes/No

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any error messages, logs, or screenshots?
      placeholder: Add any other context here...