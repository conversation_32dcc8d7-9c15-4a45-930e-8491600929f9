name: 📊 Dashboard/Extension Issue
description: Report an issue with the web dashboard or VSCode extension dashboard
title: "[Dashboard]: "
labels: ["dashboard", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Report issues with the web dashboard or VSCode extension dashboard.

  - type: dropdown
    id: dashboard-type
    attributes:
      label: Dashboard Type
      description: Which dashboard are you using?
      options:
        - "Web Dashboard"
        - "VSCode Extension Dashboard"
    validations:
      required: true

  - type: dropdown
    id: issue-type
    attributes:
      label: Issue Type
      description: What type of dashboard issue is this?
      options:
        - "Bug - Dashboard not loading"
        - "Bug - Display/UI issues"
        - "Bug - Updates not showing"
        - "Feature request"
        - "Performance issue"
        - "Other"
    validations:
      required: true

  - type: textarea
    id: issue-description
    attributes:
      label: What's happening?
      description: Describe the issue or feature request
      placeholder: Tell us what's wrong or what you'd like to see...
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: How can we reproduce this issue?
      placeholder: |
        1. Start dashboard with...
        2. Do this...
        3. See issue
    validations:
      required: true

  - type: textarea
    id: command-used
    attributes:
      label: Command or Setup Used
      description: How did you start the MCP server/dashboard or what VSCode version are you using?
      render: shell
      placeholder: npx @pimzino/spec-workflow-mcp@latest --dashboard

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any other details, error messages, or screenshots?
      placeholder: Add any other context here...