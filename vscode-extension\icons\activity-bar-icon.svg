<svg xmlns="http://www.w3.org/2000/svg"
     width="96" height="96" viewBox="0 0 96 96"
     preserveAspectRatio="xMidYMid meet"
     fill="none" stroke="currentColor" stroke-width="0.75"
     stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
  <title>Flow diagram on a page</title>

  <style>*{vector-effect:non-scaling-stroke}</style>
  <g transform="scale(4)">


  <!-- Page with folded corner -->
  <path d="M6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/>
  <path d="M14 2v6h6"/>

  <!-- Nodes (precisely aligned) -->
  <!-- Start: circle center (9,7.5), r=1.6 -> bottom tangent at y=9.1 -->
  <circle cx="9" cy="7.5" r="1.6"/>

  <!-- Process: rect from (6,11.5) size 6x3.5; center y=13.25; right mid at (12,13.25) -->
  <rect x="6" y="11.5" width="6" height="3.5" rx="0.6"/>

  <!-- Decision: diamond centered at (16.25,13.25), touching (16.25,11.5) top,
       (18,13.25) right, (16.25,15) bottom, (14.5,13.25) left -->
  <polygon points="16.25,11.5 18,13.25 16.25,15 14.5,13.25"/>

  <!-- Connectors (end exactly at tangents/edges) -->
  <!-- From circle bottom (9,9.1) to process top center (9,11.5) -->
  <line x1="9" y1="9.1" x2="9" y2="11.5"/>
  <!-- Arrowhead into the process box -->
  <polyline points="8.55,11.0 9,11.5 9.45,11.0"/>

  <!-- From process right mid (12,13.25) to diamond left point (14.5,13.25) -->
  <line x1="12" y1="13.25" x2="14.5" y2="13.25"/>
  <!-- Arrowhead into the diamond -->
  <polyline points="14.0,12.8 14.5,13.25 14.0,13.7"/>

  <!-- Optional: from diamond bottom point (16.25,15) down a bit to imply continuation -->
  <line x1="16.25" y1="15" x2="16.25" y2="16.75"/>
  <polyline points="15.8,16.25 16.25,16.75 16.7,16.25"/>

  </g>
</svg>
