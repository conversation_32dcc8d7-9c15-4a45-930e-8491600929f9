{"name": "spec-workflow-mcp", "displayName": "Spec Workflow MCP", "description": "VSCode extension for Spec-Workflow-MCP with integrated dashboard", "version": "0.0.4", "publisher": "Pimzin<PERSON>", "license": "GPL-3.0", "icon": "icon.png", "repository": {"type": "git", "url": "https://github.com/Pimzino/spec-workflow-mcp.git"}, "engines": {"vscode": "^1.99.0"}, "categories": ["Other"], "keywords": ["spec", "workflow", "mcp", "dashboard", "project management"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "spec-workflow.openDashboard", "title": "Open Spec Workflow Dashboard", "category": "Spec Workflow"}, {"command": "spec-workflow.refreshData", "title": "Refresh Data", "category": "Spec Workflow", "icon": "$(refresh)"}, {"command": "spec-workflow.openSpec", "title": "Open Spec", "category": "Spec Workflow"}, {"command": "spec-workflow.approveFromEditor", "title": "Approve", "category": "Spec Workflow"}, {"command": "spec-workflow.rejectFromEditor", "title": "Reject", "category": "Spec Workflow"}, {"command": "spec-workflow.requestRevisionFromEditor", "title": "Request Revision", "category": "Spec Workflow"}, {"command": "spec-workflow.addCommentToSelection", "title": "Add Comment", "category": "Spec Workflow", "icon": "$(comment-add)"}, {"command": "spec-workflow.editComment", "title": "Edit Comment", "category": "Spec Workflow", "icon": "$(edit)"}, {"command": "spec-workflow.deleteComment", "title": "Delete Comment", "category": "Spec Workflow", "icon": "$(trash)"}, {"command": "spec-workflow.showApprovalActions", "title": "Approval Actions", "category": "Spec Workflow"}], "views": {"spec-workflow": [{"type": "webview", "id": "spec-workflow.sidebar", "name": "Dashboard", "when": "workspaceFolderCount > 0", "icon": "./icons/activity-bar-icon.svg"}]}, "viewsContainers": {"activitybar": [{"id": "spec-workflow", "title": "Spec Workflow", "icon": "./icons/activity-bar-icon.svg"}]}, "menus": {"view/title": [{"command": "spec-workflow.refreshData", "when": "view == spec-workflow.sidebar", "group": "navigation"}], "editor/title": [{"command": "spec-workflow.addCommentToSelection", "when": "editorHasSelection", "group": "navigation", "alt": "spec-workflow.addCommentToSelection"}], "editor/context": [{"command": "spec-workflow.showApprovalActions", "when": "editorTextFocus", "group": "spec-workflow@1"}, {"command": "spec-workflow.addCommentToSelection", "when": "editorHasSelection", "group": "spec-workflow@2"}], "commandPalette": [{"command": "spec-workflow.approveFromEditor", "when": "editorTextFocus"}, {"command": "spec-workflow.rejectFromEditor", "when": "editorTextFocus"}, {"command": "spec-workflow.requestRevisionFromEditor", "when": "editorTextFocus"}, {"command": "spec-workflow.addCommentToSelection", "when": "editorHasSelection"}, {"command": "spec-workflow.showApprovalActions", "when": "editorTextFocus"}]}, "configuration": {"title": "Spec Workflow", "properties": {"specWorkflow.notifications.sounds.enabled": {"type": "boolean", "default": true, "scope": "window", "description": "Enable sound notifications for spec workflow events"}, "specWorkflow.notifications.sounds.volume": {"type": "number", "default": 0.5, "minimum": 0, "maximum": 1, "scope": "window", "description": "Volume level for sound notifications (0.0 to 1.0)"}, "specWorkflow.notifications.sounds.approvalSound": {"type": "boolean", "default": true, "scope": "window", "description": "Play sound when new approval requests are pending"}, "specWorkflow.notifications.sounds.taskCompletionSound": {"type": "boolean", "default": true, "scope": "window", "description": "Play sound when tasks are completed"}}}}, "scripts": {"vscode:prepublish": "npm run build:webview && node esbuild.js --production", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc -b --watch", "watch:webview": "npm run build:webview -- --watch", "package": "npm run check-types && npm run lint && npm run build:webview && node esbuild.js --production", "build:webview": "vite build --emptyOutDir", "dev:webview": "vite", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc -b", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/vscode": "^1.99.0", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "@vitejs/plugin-react": "^4.3.4", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "esbuild": "^0.25.0", "eslint": "^9.21.0", "npm-run-all": "^4.1.5", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.7", "typescript": "^5.7.3", "vite": "^6.0.3"}, "dependencies": {"@jaames/iro": "^5.5.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^2.6.0"}}