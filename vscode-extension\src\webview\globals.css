@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* VSCode specific styles */
body {
  margin: 0;
  padding: 0;
  font-family: var(--vscode-font-family), -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: var(--vscode-font-size, 13px);
  line-height: 1.4;
  background-color: var(--vscode-sideBar-background);
  color: var(--vscode-foreground);
}

/* VS Code Light Theme */
body.vscode-light {
  --background: var(--vscode-editor-background, #ffffff);
  --foreground: var(--vscode-foreground, #000000);
  --card: var(--vscode-editor-background, #ffffff);
  --card-foreground: var(--vscode-foreground, #000000);
  --popover: var(--vscode-dropdown-background, #ffffff);
  --popover-foreground: var(--vscode-dropdown-foreground, #000000);
  --primary: var(--vscode-button-background, #0e639c);
  --primary-foreground: var(--vscode-button-foreground, #ffffff);
  --secondary: var(--vscode-button-secondaryBackground, #f3f3f3);
  --secondary-foreground: var(--vscode-button-secondaryForeground, #000000);
  --muted: var(--vscode-input-background, #ffffff);
  --muted-foreground: var(--vscode-descriptionForeground, #717171);
  --accent: var(--vscode-focusBorder, #0e639c);
  --accent-foreground: var(--vscode-foreground, #000000);
  --destructive: var(--vscode-errorForeground, #e51400);
  --border: var(--vscode-panel-border, #d4d4d4);
  --input: var(--vscode-input-background, #ffffff);
  --ring: var(--vscode-focusBorder, #0e639c);
}

/* VS Code Dark Theme */
body.vscode-dark {
  --background: var(--vscode-editor-background, #1e1e1e);
  --foreground: var(--vscode-foreground, #cccccc);
  --card: var(--vscode-editor-background, #1e1e1e);
  --card-foreground: var(--vscode-foreground, #cccccc);
  --popover: var(--vscode-dropdown-background, #3c3c3c);
  --popover-foreground: var(--vscode-dropdown-foreground, #cccccc);
  --primary: var(--vscode-button-background, #0e639c);
  --primary-foreground: var(--vscode-button-foreground, #ffffff);
  --secondary: var(--vscode-button-secondaryBackground, #3c3c3c);
  --secondary-foreground: var(--vscode-button-secondaryForeground, #cccccc);
  --muted: var(--vscode-input-background, #3c3c3c);
  --muted-foreground: var(--vscode-descriptionForeground, #717171);
  --accent: var(--vscode-focusBorder, #007acc);
  --accent-foreground: var(--vscode-foreground, #cccccc);
  --destructive: var(--vscode-errorForeground, #f48771);
  --border: var(--vscode-panel-border, #474747);
  --input: var(--vscode-input-background, #3c3c3c);
  --ring: var(--vscode-focusBorder, #007acc);
}

/* VS Code High Contrast Theme */
body.vscode-high-contrast {
  --background: var(--vscode-editor-background, #000000);
  --foreground: var(--vscode-foreground, #ffffff);
  --card: var(--vscode-editor-background, #000000);
  --card-foreground: var(--vscode-foreground, #ffffff);
  --popover: var(--vscode-dropdown-background, #000000);
  --popover-foreground: var(--vscode-dropdown-foreground, #ffffff);
  --primary: var(--vscode-button-background, #ffffff);
  --primary-foreground: var(--vscode-button-foreground, #000000);
  --secondary: var(--vscode-button-secondaryBackground, #000000);
  --secondary-foreground: var(--vscode-button-secondaryForeground, #ffffff);
  --muted: var(--vscode-input-background, #000000);
  --muted-foreground: var(--vscode-descriptionForeground, #ffffff);
  --accent: var(--vscode-focusBorder, #ffffff);
  --accent-foreground: var(--vscode-foreground, #ffffff);
  --destructive: var(--vscode-errorForeground, #ff0000);
  --border: var(--vscode-panel-border, #ffffff);
  --input: var(--vscode-input-background, #000000);
  --ring: var(--vscode-focusBorder, #ffffff);
}

/* Compact sidebar styles */
.sidebar-root {
  padding: 8px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-sticky-header {
  position: sticky;
  top: 0;
  background: var(--vscode-sideBar-background);
  z-index: 10;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border);
  flex-shrink: 0;
}

.sidebar-scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding-top: 8px;
  min-height: 0;
}

/* Custom scrollbar for VSCode */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--vscode-scrollbarSlider-background);
}

::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-activeBackground);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Horizontal scrollable file paths */
.task-files-container {
  overflow-x: auto;
  overflow-y: visible;
  scrollbar-width: thin;
  scrollbar-color: var(--vscode-scrollbarSlider-background) transparent;
  padding-bottom: 4px; /* Space for scrollbar */
}

/* Minimalistic horizontal scrollbar */
.task-files-container::-webkit-scrollbar {
  height: 6px;
}

.task-files-container::-webkit-scrollbar-track {
  background: transparent;
}

.task-files-container::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbarSlider-background, rgba(0, 0, 0, 0.3));
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.task-files-container::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbarSlider-hoverBackground, rgba(0, 0, 0, 0.5));
}

.task-files-list {
  display: flex;
  gap: 4px;
  min-width: min-content;
  width: max-content;
}