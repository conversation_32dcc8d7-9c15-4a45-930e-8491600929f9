name: 📚 Documentation
description: Report issues with documentation or suggest improvements
title: "[Docs]: "
labels: ["documentation", "needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Help us improve the documentation!

  - type: dropdown
    id: issue-type
    attributes:
      label: Issue Type
      description: What kind of documentation issue is this?
      options:
        - "Error or typo"
        - "Unclear explanation"
        - "Missing information"
        - "Outdated information"
        - "New documentation needed"
        - "Better examples needed"
        - "Other"
    validations:
      required: true

  - type: textarea
    id: location
    attributes:
      label: Where is the issue?
      description: Which documentation has the issue?
      placeholder: |
        - File: README.md, etc.
        - Section: Installation, Usage, etc.
        - URL: If online documentation
    validations:
      required: true

  - type: textarea
    id: issue-description
    attributes:
      label: What's the problem?
      description: Describe the documentation issue
      placeholder: What's wrong or missing?
    validations:
      required: true

  - type: textarea
    id: suggested-improvement
    attributes:
      label: How should it be improved?
      description: What would make it better?
      placeholder: Your suggested improvement...
    validations:
      required: true

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Any other details that would help?
      placeholder: Add any other context here...