var Io=Object.defineProperty;var Mo=(e,t,n)=>t in e?Io(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var _t=(e,t,n)=>Mo(e,typeof t!="symbol"?t+"":t,n);import{r as l,j as s,R as he,u as Q,c as wt,a as qn,b as Oo,g as ko,d as X,S as Do,e as Lo,f as fe,h as _o,B as re,C as Ne,i as rt,k as ot,l as Ee,m as st,n as Fo}from"./globals.js";function V(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}function We(e,t=[]){let n=[];function r(i,c){const a=l.createContext(c),u=n.length;n=[...n,c];const d=h=>{const{scope:x,children:m,...g}=h,f=x?.[e]?.[u]||a,v=l.useMemo(()=>g,Object.values(g));return s.jsx(f.Provider,{value:v,children:m})};d.displayName=i+"Provider";function p(h,x){const m=x?.[e]?.[u]||a,g=l.useContext(m);if(g)return g;if(c!==void 0)return c;throw new Error(`\`${h}\` must be used within \`${i}\``)}return[d,p]}const o=()=>{const i=n.map(c=>l.createContext(c));return function(a){const u=a?.[e]||i;return l.useMemo(()=>({[`__scope${e}`]:{...a,[e]:u}}),[a,u])}};return o.scopeName=e,[r,Bo(o,...t)]}function Bo(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const c=r.reduce((a,{useScope:u,scopeName:d})=>{const h=u(i)[`__scope${d}`];return{...a,...h}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function Gn(e){const t=e+"CollectionProvider",[n,r]=We(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=f=>{const{scope:v,children:w}=f,b=he.useRef(null),S=he.useRef(new Map).current;return s.jsx(o,{scope:v,itemMap:S,collectionRef:b,children:w})};c.displayName=t;const a=e+"CollectionSlot",u=wt(a),d=he.forwardRef((f,v)=>{const{scope:w,children:b}=f,S=i(a,w),N=Q(v,S.collectionRef);return s.jsx(u,{ref:N,children:b})});d.displayName=a;const p=e+"CollectionItemSlot",h="data-radix-collection-item",x=wt(p),m=he.forwardRef((f,v)=>{const{scope:w,children:b,...S}=f,N=he.useRef(null),E=Q(v,N),j=i(p,w);return he.useEffect(()=>(j.itemMap.set(N,{ref:N,...S}),()=>void j.itemMap.delete(N))),s.jsx(x,{[h]:"",ref:E,children:b})});m.displayName=p;function g(f){const v=i(e+"CollectionConsumer",f);return he.useCallback(()=>{const b=v.collectionRef.current;if(!b)return[];const S=Array.from(b.querySelectorAll(`[${h}]`));return Array.from(v.itemMap.values()).sort((j,R)=>S.indexOf(j.ref.current)-S.indexOf(R.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:c,Slot:d,ItemSlot:m},g,r]}var ee=globalThis?.document?l.useLayoutEffect:()=>{},$o=qn[" useId ".trim().toString()]||(()=>{}),Vo=0;function Je(e){const[t,n]=l.useState($o());return ee(()=>{n(r=>r??String(Vo++))},[e]),e||(t?`radix-${t}`:"")}var et=Oo();const Ho=ko(et);var Wo=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],H=Wo.reduce((e,t)=>{const n=wt(`Primitive.${t}`),r=l.forwardRef((o,i)=>{const{asChild:c,...a}=o,u=c?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),s.jsx(u,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Uo(e,t){e&&et.flushSync(()=>e.dispatchEvent(t))}function Ae(e){const t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...n)=>t.current?.(...n),[])}var zo=qn[" useInsertionEffect ".trim().toString()]||ee;function bt({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,c]=Ko({defaultProp:t,onChange:n}),a=e!==void 0,u=a?e:o;{const p=l.useRef(e!==void 0);l.useEffect(()=>{const h=p.current;h!==a&&console.warn(`${r} is changing from ${h?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),p.current=a},[a,r])}const d=l.useCallback(p=>{if(a){const h=qo(p)?p(e):p;h!==e&&c.current?.(h)}else i(p)},[a,e,i,c]);return[u,d]}function Ko({defaultProp:e,onChange:t}){const[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return zo(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}function qo(e){return typeof e=="function"}var Go=l.createContext(void 0);function sn(e){const t=l.useContext(Go);return e||t||"ltr"}var Ft="rovingFocusGroup.onEntryFocus",Yo={bubbles:!1,cancelable:!0},tt="RovingFocusGroup",[Gt,Yn,Xo]=Gn(tt),[Zo,Xn]=We(tt,[Xo]),[Qo,Jo]=Zo(tt),Zn=l.forwardRef((e,t)=>s.jsx(Gt.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(Gt.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(es,{...e,ref:t})})}));Zn.displayName=tt;var es=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:c,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:u,onEntryFocus:d,preventScrollOnEntryFocus:p=!1,...h}=e,x=l.useRef(null),m=Q(t,x),g=sn(i),[f,v]=bt({prop:c,defaultProp:a??null,onChange:u,caller:tt}),[w,b]=l.useState(!1),S=Ae(d),N=Yn(n),E=l.useRef(!1),[j,R]=l.useState(0);return l.useEffect(()=>{const A=x.current;if(A)return A.addEventListener(Ft,S),()=>A.removeEventListener(Ft,S)},[S]),s.jsx(Qo,{scope:n,orientation:r,dir:g,loop:o,currentTabStopId:f,onItemFocus:l.useCallback(A=>v(A),[v]),onItemShiftTab:l.useCallback(()=>b(!0),[]),onFocusableItemAdd:l.useCallback(()=>R(A=>A+1),[]),onFocusableItemRemove:l.useCallback(()=>R(A=>A-1),[]),children:s.jsx(H.div,{tabIndex:w||j===0?-1:0,"data-orientation":r,...h,ref:m,style:{outline:"none",...e.style},onMouseDown:V(e.onMouseDown,()=>{E.current=!0}),onFocus:V(e.onFocus,A=>{const L=!E.current;if(A.target===A.currentTarget&&L&&!w){const O=new CustomEvent(Ft,Yo);if(A.currentTarget.dispatchEvent(O),!O.defaultPrevented){const D=N().filter(T=>T.focusable),$=D.find(T=>T.active),_=D.find(T=>T.id===f),U=[$,_,...D].filter(Boolean).map(T=>T.ref.current);er(U,p)}}E.current=!1}),onBlur:V(e.onBlur,()=>b(!1))})})}),Qn="RovingFocusGroupItem",Jn=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:c,...a}=e,u=Je(),d=i||u,p=Jo(Qn,n),h=p.currentTabStopId===d,x=Yn(n),{onFocusableItemAdd:m,onFocusableItemRemove:g,currentTabStopId:f}=p;return l.useEffect(()=>{if(r)return m(),()=>g()},[r,m,g]),s.jsx(Gt.ItemSlot,{scope:n,id:d,focusable:r,active:o,children:s.jsx(H.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...a,ref:t,onMouseDown:V(e.onMouseDown,v=>{r?p.onItemFocus(d):v.preventDefault()}),onFocus:V(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:V(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){p.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const w=rs(v,p.orientation,p.dir);if(w!==void 0){if(v.metaKey||v.ctrlKey||v.altKey||v.shiftKey)return;v.preventDefault();let S=x().filter(N=>N.focusable).map(N=>N.ref.current);if(w==="last")S.reverse();else if(w==="prev"||w==="next"){w==="prev"&&S.reverse();const N=S.indexOf(v.currentTarget);S=p.loop?os(S,N+1):S.slice(N+1)}setTimeout(()=>er(S))}}),children:typeof c=="function"?c({isCurrentTabStop:h,hasTabStop:f!=null}):c})})});Jn.displayName=Qn;var ts={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function ns(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function rs(e,t,n){const r=ns(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return ts[r]}function er(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function os(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var ss=Zn,is=Jn;function as(e,t){return l.useReducer((n,r)=>t[n][r]??n,e)}var tr=e=>{const{present:t,children:n}=e,r=cs(t),o=typeof n=="function"?n({present:r.isPresent}):l.Children.only(n),i=Q(r.ref,ls(o));return typeof n=="function"||r.isPresent?l.cloneElement(o,{ref:i}):null};tr.displayName="Presence";function cs(e){const[t,n]=l.useState(),r=l.useRef(null),o=l.useRef(e),i=l.useRef("none"),c=e?"mounted":"unmounted",[a,u]=as(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return l.useEffect(()=>{const d=it(r.current);i.current=a==="mounted"?d:"none"},[a]),ee(()=>{const d=r.current,p=o.current;if(p!==e){const x=i.current,m=it(d);e?u("MOUNT"):m==="none"||d?.display==="none"?u("UNMOUNT"):u(p&&x!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),ee(()=>{if(t){let d;const p=t.ownerDocument.defaultView??window,h=m=>{const f=it(r.current).includes(CSS.escape(m.animationName));if(m.target===t&&f&&(u("ANIMATION_END"),!o.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",d=p.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},x=m=>{m.target===t&&(i.current=it(r.current))};return t.addEventListener("animationstart",x),t.addEventListener("animationcancel",h),t.addEventListener("animationend",h),()=>{p.clearTimeout(d),t.removeEventListener("animationstart",x),t.removeEventListener("animationcancel",h),t.removeEventListener("animationend",h)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:l.useCallback(d=>{r.current=d?getComputedStyle(d):null,n(d)},[])}}function it(e){return e?.animationName||"none"}function ls(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Pt="Tabs",[us,Jc]=We(Pt,[Xn]),nr=Xn(),[ds,an]=us(Pt),rr=l.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:c="horizontal",dir:a,activationMode:u="automatic",...d}=e,p=sn(a),[h,x]=bt({prop:r,onChange:o,defaultProp:i??"",caller:Pt});return s.jsx(ds,{scope:n,baseId:Je(),value:h,onValueChange:x,orientation:c,dir:p,activationMode:u,children:s.jsx(H.div,{dir:p,"data-orientation":c,...d,ref:t})})});rr.displayName=Pt;var or="TabsList",sr=l.forwardRef((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,i=an(or,n),c=nr(n);return s.jsx(ss,{asChild:!0,...c,orientation:i.orientation,dir:i.dir,loop:r,children:s.jsx(H.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});sr.displayName=or;var ir="TabsTrigger",ar=l.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,c=an(ir,n),a=nr(n),u=ur(c.baseId,r),d=dr(c.baseId,r),p=r===c.value;return s.jsx(is,{asChild:!0,...a,focusable:!o,active:p,children:s.jsx(H.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":d,"data-state":p?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:u,...i,ref:t,onMouseDown:V(e.onMouseDown,h=>{!o&&h.button===0&&h.ctrlKey===!1?c.onValueChange(r):h.preventDefault()}),onKeyDown:V(e.onKeyDown,h=>{[" ","Enter"].includes(h.key)&&c.onValueChange(r)}),onFocus:V(e.onFocus,()=>{const h=c.activationMode!=="manual";!p&&!o&&h&&c.onValueChange(r)})})})});ar.displayName=ir;var cr="TabsContent",lr=l.forwardRef((e,t)=>{const{__scopeTabs:n,value:r,forceMount:o,children:i,...c}=e,a=an(cr,n),u=ur(a.baseId,r),d=dr(a.baseId,r),p=r===a.value,h=l.useRef(p);return l.useEffect(()=>{const x=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(x)},[]),s.jsx(tr,{present:o||p,children:({present:x})=>s.jsx(H.div,{"data-state":p?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":u,hidden:!x,id:d,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:x&&i})})});lr.displayName=cr;function ur(e,t){return`${e}-trigger-${t}`}function dr(e,t){return`${e}-content-${t}`}var fs=rr,ps=sr,ms=ar,hs=lr;function vs({className:e,...t}){return s.jsx(fs,{"data-slot":"tabs",className:X("flex flex-col gap-2",e),...t})}function gs({className:e,...t}){return s.jsx(ps,{"data-slot":"tabs-list",className:X("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function Ge({className:e,...t}){return s.jsx(ms,{"data-slot":"tabs-trigger",className:X("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function Ye({className:e,...t}){return s.jsx(hs,{"data-slot":"tabs-content",className:X("flex-1 outline-none",e),...t})}const xs=Lo("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function at({className:e,variant:t,asChild:n=!1,...r}){const o=n?Do:"span";return s.jsx(o,{"data-slot":"badge",className:X(xs({variant:t}),e),...r})}var cn="Progress",ln=100,[ys,el]=We(cn),[ws,bs]=ys(cn),fr=l.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:i=Ss,...c}=e;(o||o===0)&&!wn(o)&&console.error(Cs(`${o}`,"Progress"));const a=wn(o)?o:ln;r!==null&&!bn(r,a)&&console.error(Ns(`${r}`,"Progress"));const u=bn(r,a)?r:null,d=St(u)?i(u,a):void 0;return s.jsx(ws,{scope:n,value:u,max:a,children:s.jsx(H.div,{"aria-valuemax":a,"aria-valuemin":0,"aria-valuenow":St(u)?u:void 0,"aria-valuetext":d,role:"progressbar","data-state":hr(u,a),"data-value":u??void 0,"data-max":a,...c,ref:t})})});fr.displayName=cn;var pr="ProgressIndicator",mr=l.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=bs(pr,n);return s.jsx(H.div,{"data-state":hr(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});mr.displayName=pr;function Ss(e,t){return`${Math.round(e/t*100)}%`}function hr(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function St(e){return typeof e=="number"}function wn(e){return St(e)&&!isNaN(e)&&e>0}function bn(e,t){return St(e)&&!isNaN(e)&&e<=t&&e>=0}function Cs(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${ln}\`.`}function Ns(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${ln} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Es=fr,Rs=mr;function Sn({className:e,value:t,...n}){return s.jsx(Es,{"data-slot":"progress",className:X("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...n,children:s.jsx(Rs,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}function Cn(e,[t,n]){return Math.min(n,Math.max(t,e))}function As(e,t=globalThis?.document){const n=Ae(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ps="DismissableLayer",Yt="dismissableLayer.update",js="dismissableLayer.pointerDownOutside",Ts="dismissableLayer.focusOutside",Nn,vr=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),gr=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:c,onDismiss:a,...u}=e,d=l.useContext(vr),[p,h]=l.useState(null),x=p?.ownerDocument??globalThis?.document,[,m]=l.useState({}),g=Q(t,R=>h(R)),f=Array.from(d.layers),[v]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),w=f.indexOf(v),b=p?f.indexOf(p):-1,S=d.layersWithOutsidePointerEventsDisabled.size>0,N=b>=w,E=Os(R=>{const A=R.target,L=[...d.branches].some(O=>O.contains(A));!N||L||(o?.(R),c?.(R),R.defaultPrevented||a?.())},x),j=ks(R=>{const A=R.target;[...d.branches].some(O=>O.contains(A))||(i?.(R),c?.(R),R.defaultPrevented||a?.())},x);return As(R=>{b===d.layers.size-1&&(r?.(R),!R.defaultPrevented&&a&&(R.preventDefault(),a()))},x),l.useEffect(()=>{if(p)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(Nn=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(p)),d.layers.add(p),En(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(x.body.style.pointerEvents=Nn)}},[p,x,n,d]),l.useEffect(()=>()=>{p&&(d.layers.delete(p),d.layersWithOutsidePointerEventsDisabled.delete(p),En())},[p,d]),l.useEffect(()=>{const R=()=>m({});return document.addEventListener(Yt,R),()=>document.removeEventListener(Yt,R)},[]),s.jsx(H.div,{...u,ref:g,style:{pointerEvents:S?N?"auto":"none":void 0,...e.style},onFocusCapture:V(e.onFocusCapture,j.onFocusCapture),onBlurCapture:V(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:V(e.onPointerDownCapture,E.onPointerDownCapture)})});gr.displayName=Ps;var Is="DismissableLayerBranch",Ms=l.forwardRef((e,t)=>{const n=l.useContext(vr),r=l.useRef(null),o=Q(t,r);return l.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),s.jsx(H.div,{...e,ref:o})});Ms.displayName=Is;function Os(e,t=globalThis?.document){const n=Ae(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let u=function(){xr(js,n,d,{discrete:!0})};const d={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function ks(e,t=globalThis?.document){const n=Ae(e),r=l.useRef(!1);return l.useEffect(()=>{const o=i=>{i.target&&!r.current&&xr(Ts,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function En(){const e=new CustomEvent(Yt);document.dispatchEvent(e)}function xr(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Uo(o,i):o.dispatchEvent(i)}var Bt=0;function Ds(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Rn()),document.body.insertAdjacentElement("beforeend",e[1]??Rn()),Bt++,()=>{Bt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Bt--}},[])}function Rn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var $t="focusScope.autoFocusOnMount",Vt="focusScope.autoFocusOnUnmount",An={bubbles:!1,cancelable:!0},Ls="FocusScope",yr=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...c}=e,[a,u]=l.useState(null),d=Ae(o),p=Ae(i),h=l.useRef(null),x=Q(t,f=>u(f)),m=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let f=function(S){if(m.paused||!a)return;const N=S.target;a.contains(N)?h.current=N:Re(h.current,{select:!0})},v=function(S){if(m.paused||!a)return;const N=S.relatedTarget;N!==null&&(a.contains(N)||Re(h.current,{select:!0}))},w=function(S){if(document.activeElement===document.body)for(const E of S)E.removedNodes.length>0&&Re(a)};document.addEventListener("focusin",f),document.addEventListener("focusout",v);const b=new MutationObserver(w);return a&&b.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",f),document.removeEventListener("focusout",v),b.disconnect()}}},[r,a,m.paused]),l.useEffect(()=>{if(a){jn.add(m);const f=document.activeElement;if(!a.contains(f)){const w=new CustomEvent($t,An);a.addEventListener($t,d),a.dispatchEvent(w),w.defaultPrevented||(_s(Hs(wr(a)),{select:!0}),document.activeElement===f&&Re(a))}return()=>{a.removeEventListener($t,d),setTimeout(()=>{const w=new CustomEvent(Vt,An);a.addEventListener(Vt,p),a.dispatchEvent(w),w.defaultPrevented||Re(f??document.body,{select:!0}),a.removeEventListener(Vt,p),jn.remove(m)},0)}}},[a,d,p,m]);const g=l.useCallback(f=>{if(!n&&!r||m.paused)return;const v=f.key==="Tab"&&!f.altKey&&!f.ctrlKey&&!f.metaKey,w=document.activeElement;if(v&&w){const b=f.currentTarget,[S,N]=Fs(b);S&&N?!f.shiftKey&&w===N?(f.preventDefault(),n&&Re(S,{select:!0})):f.shiftKey&&w===S&&(f.preventDefault(),n&&Re(N,{select:!0})):w===b&&f.preventDefault()}},[n,r,m.paused]);return s.jsx(H.div,{tabIndex:-1,...c,ref:x,onKeyDown:g})});yr.displayName=Ls;function _s(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Re(r,{select:t}),document.activeElement!==n)return}function Fs(e){const t=wr(e),n=Pn(t,e),r=Pn(t.reverse(),e);return[n,r]}function wr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Pn(e,t){for(const n of e)if(!Bs(n,{upTo:t}))return n}function Bs(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function $s(e){return e instanceof HTMLInputElement&&"select"in e}function Re(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&$s(e)&&t&&e.select()}}var jn=Vs();function Vs(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Tn(e,t),e.unshift(t)},remove(t){e=Tn(e,t),e[0]?.resume()}}}function Tn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Hs(e){return e.filter(t=>t.tagName!=="A")}const Ws=["top","right","bottom","left"],Pe=Math.min,oe=Math.max,Ct=Math.round,ct=Math.floor,xe=e=>({x:e,y:e}),Us={left:"right",right:"left",bottom:"top",top:"bottom"},zs={start:"end",end:"start"};function Xt(e,t,n){return oe(e,Pe(t,n))}function be(e,t){return typeof e=="function"?e(t):e}function Se(e){return e.split("-")[0]}function Ue(e){return e.split("-")[1]}function un(e){return e==="x"?"y":"x"}function dn(e){return e==="y"?"height":"width"}const Ks=new Set(["top","bottom"]);function ge(e){return Ks.has(Se(e))?"y":"x"}function fn(e){return un(ge(e))}function qs(e,t,n){n===void 0&&(n=!1);const r=Ue(e),o=fn(e),i=dn(o);let c=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(c=Nt(c)),[c,Nt(c)]}function Gs(e){const t=Nt(e);return[Zt(e),t,Zt(t)]}function Zt(e){return e.replace(/start|end/g,t=>zs[t])}const In=["left","right"],Mn=["right","left"],Ys=["top","bottom"],Xs=["bottom","top"];function Zs(e,t,n){switch(e){case"top":case"bottom":return n?t?Mn:In:t?In:Mn;case"left":case"right":return t?Ys:Xs;default:return[]}}function Qs(e,t,n,r){const o=Ue(e);let i=Zs(Se(e),n==="start",r);return o&&(i=i.map(c=>c+"-"+o),t&&(i=i.concat(i.map(Zt)))),i}function Nt(e){return e.replace(/left|right|bottom|top/g,t=>Us[t])}function Js(e){return{top:0,right:0,bottom:0,left:0,...e}}function br(e){return typeof e!="number"?Js(e):{top:e,right:e,bottom:e,left:e}}function Et(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function On(e,t,n){let{reference:r,floating:o}=e;const i=ge(t),c=fn(t),a=dn(c),u=Se(t),d=i==="y",p=r.x+r.width/2-o.width/2,h=r.y+r.height/2-o.height/2,x=r[a]/2-o[a]/2;let m;switch(u){case"top":m={x:p,y:r.y-o.height};break;case"bottom":m={x:p,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:h};break;case"left":m={x:r.x-o.width,y:h};break;default:m={x:r.x,y:r.y}}switch(Ue(t)){case"start":m[c]-=x*(n&&d?-1:1);break;case"end":m[c]+=x*(n&&d?-1:1);break}return m}const ei=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:c}=n,a=i.filter(Boolean),u=await(c.isRTL==null?void 0:c.isRTL(t));let d=await c.getElementRects({reference:e,floating:t,strategy:o}),{x:p,y:h}=On(d,r,u),x=r,m={},g=0;for(let f=0;f<a.length;f++){const{name:v,fn:w}=a[f],{x:b,y:S,data:N,reset:E}=await w({x:p,y:h,initialPlacement:r,placement:x,strategy:o,middlewareData:m,rects:d,platform:c,elements:{reference:e,floating:t}});p=b??p,h=S??h,m={...m,[v]:{...m[v],...N}},E&&g<=50&&(g++,typeof E=="object"&&(E.placement&&(x=E.placement),E.rects&&(d=E.rects===!0?await c.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:p,y:h}=On(d,x,u)),f=-1)}return{x:p,y:h,placement:x,strategy:o,middlewareData:m}};async function Ze(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:c,elements:a,strategy:u}=e,{boundary:d="clippingAncestors",rootBoundary:p="viewport",elementContext:h="floating",altBoundary:x=!1,padding:m=0}=be(t,e),g=br(m),v=a[x?h==="floating"?"reference":"floating":h],w=Et(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:d,rootBoundary:p,strategy:u})),b=h==="floating"?{x:r,y:o,width:c.floating.width,height:c.floating.height}:c.reference,S=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),N=await(i.isElement==null?void 0:i.isElement(S))?await(i.getScale==null?void 0:i.getScale(S))||{x:1,y:1}:{x:1,y:1},E=Et(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:S,strategy:u}):b);return{top:(w.top-E.top+g.top)/N.y,bottom:(E.bottom-w.bottom+g.bottom)/N.y,left:(w.left-E.left+g.left)/N.x,right:(E.right-w.right+g.right)/N.x}}const ti=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:c,elements:a,middlewareData:u}=t,{element:d,padding:p=0}=be(e,t)||{};if(d==null)return{};const h=br(p),x={x:n,y:r},m=fn(o),g=dn(m),f=await c.getDimensions(d),v=m==="y",w=v?"top":"left",b=v?"bottom":"right",S=v?"clientHeight":"clientWidth",N=i.reference[g]+i.reference[m]-x[m]-i.floating[g],E=x[m]-i.reference[m],j=await(c.getOffsetParent==null?void 0:c.getOffsetParent(d));let R=j?j[S]:0;(!R||!await(c.isElement==null?void 0:c.isElement(j)))&&(R=a.floating[S]||i.floating[g]);const A=N/2-E/2,L=R/2-f[g]/2-1,O=Pe(h[w],L),D=Pe(h[b],L),$=O,_=R-f[g]-D,F=R/2-f[g]/2+A,U=Xt($,F,_),T=!u.arrow&&Ue(o)!=null&&F!==U&&i.reference[g]/2-(F<$?O:D)-f[g]/2<0,B=T?F<$?F-$:F-_:0;return{[m]:x[m]+B,data:{[m]:U,centerOffset:F-U-B,...T&&{alignmentOffset:B}},reset:T}}}),ni=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:c,initialPlacement:a,platform:u,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:x,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:f=!0,...v}=be(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const w=Se(o),b=ge(a),S=Se(a)===a,N=await(u.isRTL==null?void 0:u.isRTL(d.floating)),E=x||(S||!f?[Nt(a)]:Gs(a)),j=g!=="none";!x&&j&&E.push(...Qs(a,f,g,N));const R=[a,...E],A=await Ze(t,v),L=[];let O=((r=i.flip)==null?void 0:r.overflows)||[];if(p&&L.push(A[w]),h){const F=qs(o,c,N);L.push(A[F[0]],A[F[1]])}if(O=[...O,{placement:o,overflows:L}],!L.every(F=>F<=0)){var D,$;const F=(((D=i.flip)==null?void 0:D.index)||0)+1,U=R[F];if(U&&(!(h==="alignment"?b!==ge(U):!1)||O.every(I=>ge(I.placement)===b?I.overflows[0]>0:!0)))return{data:{index:F,overflows:O},reset:{placement:U}};let T=($=O.filter(B=>B.overflows[0]<=0).sort((B,I)=>B.overflows[1]-I.overflows[1])[0])==null?void 0:$.placement;if(!T)switch(m){case"bestFit":{var _;const B=(_=O.filter(I=>{if(j){const z=ge(I.placement);return z===b||z==="y"}return!0}).map(I=>[I.placement,I.overflows.filter(z=>z>0).reduce((z,W)=>z+W,0)]).sort((I,z)=>I[1]-z[1])[0])==null?void 0:_[0];B&&(T=B);break}case"initialPlacement":T=a;break}if(o!==T)return{reset:{placement:T}}}return{}}}};function kn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Dn(e){return Ws.some(t=>e[t]>=0)}const ri=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=be(e,t);switch(r){case"referenceHidden":{const i=await Ze(t,{...o,elementContext:"reference"}),c=kn(i,n.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:Dn(c)}}}case"escaped":{const i=await Ze(t,{...o,altBoundary:!0}),c=kn(i,n.floating);return{data:{escapedOffsets:c,escaped:Dn(c)}}}default:return{}}}}},Sr=new Set(["left","top"]);async function oi(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),c=Se(n),a=Ue(n),u=ge(n)==="y",d=Sr.has(c)?-1:1,p=i&&u?-1:1,h=be(t,e);let{mainAxis:x,crossAxis:m,alignmentAxis:g}=typeof h=="number"?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&typeof g=="number"&&(m=a==="end"?g*-1:g),u?{x:m*p,y:x*d}:{x:x*d,y:m*p}}const si=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:c,middlewareData:a}=t,u=await oi(t,e);return c===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:c}}}}},ii=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:c=!1,limiter:a={fn:v=>{let{x:w,y:b}=v;return{x:w,y:b}}},...u}=be(e,t),d={x:n,y:r},p=await Ze(t,u),h=ge(Se(o)),x=un(h);let m=d[x],g=d[h];if(i){const v=x==="y"?"top":"left",w=x==="y"?"bottom":"right",b=m+p[v],S=m-p[w];m=Xt(b,m,S)}if(c){const v=h==="y"?"top":"left",w=h==="y"?"bottom":"right",b=g+p[v],S=g-p[w];g=Xt(b,g,S)}const f=a.fn({...t,[x]:m,[h]:g});return{...f,data:{x:f.x-n,y:f.y-r,enabled:{[x]:i,[h]:c}}}}}},ai=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:c}=t,{offset:a=0,mainAxis:u=!0,crossAxis:d=!0}=be(e,t),p={x:n,y:r},h=ge(o),x=un(h);let m=p[x],g=p[h];const f=be(a,t),v=typeof f=="number"?{mainAxis:f,crossAxis:0}:{mainAxis:0,crossAxis:0,...f};if(u){const S=x==="y"?"height":"width",N=i.reference[x]-i.floating[S]+v.mainAxis,E=i.reference[x]+i.reference[S]-v.mainAxis;m<N?m=N:m>E&&(m=E)}if(d){var w,b;const S=x==="y"?"width":"height",N=Sr.has(Se(o)),E=i.reference[h]-i.floating[S]+(N&&((w=c.offset)==null?void 0:w[h])||0)+(N?0:v.crossAxis),j=i.reference[h]+i.reference[S]+(N?0:((b=c.offset)==null?void 0:b[h])||0)-(N?v.crossAxis:0);g<E?g=E:g>j&&(g=j)}return{[x]:m,[h]:g}}}},ci=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:c,elements:a}=t,{apply:u=()=>{},...d}=be(e,t),p=await Ze(t,d),h=Se(o),x=Ue(o),m=ge(o)==="y",{width:g,height:f}=i.floating;let v,w;h==="top"||h==="bottom"?(v=h,w=x===(await(c.isRTL==null?void 0:c.isRTL(a.floating))?"start":"end")?"left":"right"):(w=h,v=x==="end"?"top":"bottom");const b=f-p.top-p.bottom,S=g-p.left-p.right,N=Pe(f-p[v],b),E=Pe(g-p[w],S),j=!t.middlewareData.shift;let R=N,A=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(A=S),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(R=b),j&&!x){const O=oe(p.left,0),D=oe(p.right,0),$=oe(p.top,0),_=oe(p.bottom,0);m?A=g-2*(O!==0||D!==0?O+D:oe(p.left,p.right)):R=f-2*($!==0||_!==0?$+_:oe(p.top,p.bottom))}await u({...t,availableWidth:A,availableHeight:R});const L=await c.getDimensions(a.floating);return g!==L.width||f!==L.height?{reset:{rects:!0}}:{}}}};function jt(){return typeof window<"u"}function ze(e){return Cr(e)?(e.nodeName||"").toLowerCase():"#document"}function se(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function we(e){var t;return(t=(Cr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Cr(e){return jt()?e instanceof Node||e instanceof se(e).Node:!1}function ue(e){return jt()?e instanceof Element||e instanceof se(e).Element:!1}function ye(e){return jt()?e instanceof HTMLElement||e instanceof se(e).HTMLElement:!1}function Ln(e){return!jt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof se(e).ShadowRoot}const li=new Set(["inline","contents"]);function nt(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=de(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!li.has(o)}const ui=new Set(["table","td","th"]);function di(e){return ui.has(ze(e))}const fi=[":popover-open",":modal"];function Tt(e){return fi.some(t=>{try{return e.matches(t)}catch{return!1}})}const pi=["transform","translate","scale","rotate","perspective"],mi=["transform","translate","scale","rotate","perspective","filter"],hi=["paint","layout","strict","content"];function pn(e){const t=mn(),n=ue(e)?de(e):e;return pi.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||mi.some(r=>(n.willChange||"").includes(r))||hi.some(r=>(n.contain||"").includes(r))}function vi(e){let t=je(e);for(;ye(t)&&!He(t);){if(pn(t))return t;if(Tt(t))return null;t=je(t)}return null}function mn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const gi=new Set(["html","body","#document"]);function He(e){return gi.has(ze(e))}function de(e){return se(e).getComputedStyle(e)}function It(e){return ue(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function je(e){if(ze(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ln(e)&&e.host||we(e);return Ln(t)?t.host:t}function Nr(e){const t=je(e);return He(t)?e.ownerDocument?e.ownerDocument.body:e.body:ye(t)&&nt(t)?t:Nr(t)}function Qe(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Nr(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),c=se(o);if(i){const a=Qt(c);return t.concat(c,c.visualViewport||[],nt(o)?o:[],a&&n?Qe(a):[])}return t.concat(o,Qe(o,[],n))}function Qt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Er(e){const t=de(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ye(e),i=o?e.offsetWidth:n,c=o?e.offsetHeight:r,a=Ct(n)!==i||Ct(r)!==c;return a&&(n=i,r=c),{width:n,height:r,$:a}}function hn(e){return ue(e)?e:e.contextElement}function $e(e){const t=hn(e);if(!ye(t))return xe(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Er(t);let c=(i?Ct(n.width):n.width)/r,a=(i?Ct(n.height):n.height)/o;return(!c||!Number.isFinite(c))&&(c=1),(!a||!Number.isFinite(a))&&(a=1),{x:c,y:a}}const xi=xe(0);function Rr(e){const t=se(e);return!mn()||!t.visualViewport?xi:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function yi(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==se(e)?!1:t}function Oe(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=hn(e);let c=xe(1);t&&(r?ue(r)&&(c=$e(r)):c=$e(e));const a=yi(i,n,r)?Rr(i):xe(0);let u=(o.left+a.x)/c.x,d=(o.top+a.y)/c.y,p=o.width/c.x,h=o.height/c.y;if(i){const x=se(i),m=r&&ue(r)?se(r):r;let g=x,f=Qt(g);for(;f&&r&&m!==g;){const v=$e(f),w=f.getBoundingClientRect(),b=de(f),S=w.left+(f.clientLeft+parseFloat(b.paddingLeft))*v.x,N=w.top+(f.clientTop+parseFloat(b.paddingTop))*v.y;u*=v.x,d*=v.y,p*=v.x,h*=v.y,u+=S,d+=N,g=se(f),f=Qt(g)}}return Et({width:p,height:h,x:u,y:d})}function vn(e,t){const n=It(e).scrollLeft;return t?t.left+n:Oe(we(e)).left+n}function Ar(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:vn(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function wi(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",c=we(r),a=t?Tt(t.floating):!1;if(r===c||a&&i)return n;let u={scrollLeft:0,scrollTop:0},d=xe(1);const p=xe(0),h=ye(r);if((h||!h&&!i)&&((ze(r)!=="body"||nt(c))&&(u=It(r)),ye(r))){const m=Oe(r);d=$e(r),p.x=m.x+r.clientLeft,p.y=m.y+r.clientTop}const x=c&&!h&&!i?Ar(c,u,!0):xe(0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-u.scrollLeft*d.x+p.x+x.x,y:n.y*d.y-u.scrollTop*d.y+p.y+x.y}}function bi(e){return Array.from(e.getClientRects())}function Si(e){const t=we(e),n=It(e),r=e.ownerDocument.body,o=oe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=oe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let c=-n.scrollLeft+vn(e);const a=-n.scrollTop;return de(r).direction==="rtl"&&(c+=oe(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:c,y:a}}function Ci(e,t){const n=se(e),r=we(e),o=n.visualViewport;let i=r.clientWidth,c=r.clientHeight,a=0,u=0;if(o){i=o.width,c=o.height;const d=mn();(!d||d&&t==="fixed")&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:c,x:a,y:u}}const Ni=new Set(["absolute","fixed"]);function Ei(e,t){const n=Oe(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ye(e)?$e(e):xe(1),c=e.clientWidth*i.x,a=e.clientHeight*i.y,u=o*i.x,d=r*i.y;return{width:c,height:a,x:u,y:d}}function _n(e,t,n){let r;if(t==="viewport")r=Ci(e,n);else if(t==="document")r=Si(we(e));else if(ue(t))r=Ei(t,n);else{const o=Rr(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Et(r)}function Pr(e,t){const n=je(e);return n===t||!ue(n)||He(n)?!1:de(n).position==="fixed"||Pr(n,t)}function Ri(e,t){const n=t.get(e);if(n)return n;let r=Qe(e,[],!1).filter(a=>ue(a)&&ze(a)!=="body"),o=null;const i=de(e).position==="fixed";let c=i?je(e):e;for(;ue(c)&&!He(c);){const a=de(c),u=pn(c);!u&&a.position==="fixed"&&(o=null),(i?!u&&!o:!u&&a.position==="static"&&!!o&&Ni.has(o.position)||nt(c)&&!u&&Pr(e,c))?r=r.filter(p=>p!==c):o=a,c=je(c)}return t.set(e,r),r}function Ai(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const c=[...n==="clippingAncestors"?Tt(t)?[]:Ri(t,this._c):[].concat(n),r],a=c[0],u=c.reduce((d,p)=>{const h=_n(t,p,o);return d.top=oe(h.top,d.top),d.right=Pe(h.right,d.right),d.bottom=Pe(h.bottom,d.bottom),d.left=oe(h.left,d.left),d},_n(t,a,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function Pi(e){const{width:t,height:n}=Er(e);return{width:t,height:n}}function ji(e,t,n){const r=ye(t),o=we(t),i=n==="fixed",c=Oe(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const u=xe(0);function d(){u.x=vn(o)}if(r||!r&&!i)if((ze(t)!=="body"||nt(o))&&(a=It(t)),r){const m=Oe(t,!0,i,t);u.x=m.x+t.clientLeft,u.y=m.y+t.clientTop}else o&&d();i&&!r&&o&&d();const p=o&&!r&&!i?Ar(o,a):xe(0),h=c.left+a.scrollLeft-u.x-p.x,x=c.top+a.scrollTop-u.y-p.y;return{x:h,y:x,width:c.width,height:c.height}}function Ht(e){return de(e).position==="static"}function Fn(e,t){if(!ye(e)||de(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return we(e)===n&&(n=n.ownerDocument.body),n}function jr(e,t){const n=se(e);if(Tt(e))return n;if(!ye(e)){let o=je(e);for(;o&&!He(o);){if(ue(o)&&!Ht(o))return o;o=je(o)}return n}let r=Fn(e,t);for(;r&&di(r)&&Ht(r);)r=Fn(r,t);return r&&He(r)&&Ht(r)&&!pn(r)?n:r||vi(e)||n}const Ti=async function(e){const t=this.getOffsetParent||jr,n=this.getDimensions,r=await n(e.floating);return{reference:ji(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ii(e){return de(e).direction==="rtl"}const Mi={convertOffsetParentRelativeRectToViewportRelativeRect:wi,getDocumentElement:we,getClippingRect:Ai,getOffsetParent:jr,getElementRects:Ti,getClientRects:bi,getDimensions:Pi,getScale:$e,isElement:ue,isRTL:Ii};function Tr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Oi(e,t){let n=null,r;const o=we(e);function i(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function c(a,u){a===void 0&&(a=!1),u===void 0&&(u=1),i();const d=e.getBoundingClientRect(),{left:p,top:h,width:x,height:m}=d;if(a||t(),!x||!m)return;const g=ct(h),f=ct(o.clientWidth-(p+x)),v=ct(o.clientHeight-(h+m)),w=ct(p),S={rootMargin:-g+"px "+-f+"px "+-v+"px "+-w+"px",threshold:oe(0,Pe(1,u))||1};let N=!0;function E(j){const R=j[0].intersectionRatio;if(R!==u){if(!N)return c();R?c(!1,R):r=setTimeout(()=>{c(!1,1e-7)},1e3)}R===1&&!Tr(d,e.getBoundingClientRect())&&c(),N=!1}try{n=new IntersectionObserver(E,{...S,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,S)}n.observe(e)}return c(!0),i}function ki(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,d=hn(e),p=o||i?[...d?Qe(d):[],...Qe(t)]:[];p.forEach(w=>{o&&w.addEventListener("scroll",n,{passive:!0}),i&&w.addEventListener("resize",n)});const h=d&&a?Oi(d,n):null;let x=-1,m=null;c&&(m=new ResizeObserver(w=>{let[b]=w;b&&b.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var S;(S=m)==null||S.observe(t)})),n()}),d&&!u&&m.observe(d),m.observe(t));let g,f=u?Oe(e):null;u&&v();function v(){const w=Oe(e);f&&!Tr(f,w)&&n(),f=w,g=requestAnimationFrame(v)}return n(),()=>{var w;p.forEach(b=>{o&&b.removeEventListener("scroll",n),i&&b.removeEventListener("resize",n)}),h?.(),(w=m)==null||w.disconnect(),m=null,u&&cancelAnimationFrame(g)}}const Di=si,Li=ii,_i=ni,Fi=ci,Bi=ri,Bn=ti,$i=ai,Vi=(e,t,n)=>{const r=new Map,o={platform:Mi,...n},i={...o.platform,_c:r};return ei(e,t,{...o,platform:i})};var Hi=typeof document<"u",Wi=function(){},gt=Hi?l.useLayoutEffect:Wi;function Rt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Rt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Rt(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Ir(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function $n(e,t){const n=Ir(e);return Math.round(t*n)/n}function Wt(e){const t=l.useRef(e);return gt(()=>{t.current=e}),t}function Ui(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:c}={},transform:a=!0,whileElementsMounted:u,open:d}=e,[p,h]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[x,m]=l.useState(r);Rt(x,r)||m(r);const[g,f]=l.useState(null),[v,w]=l.useState(null),b=l.useCallback(I=>{I!==j.current&&(j.current=I,f(I))},[]),S=l.useCallback(I=>{I!==R.current&&(R.current=I,w(I))},[]),N=i||g,E=c||v,j=l.useRef(null),R=l.useRef(null),A=l.useRef(p),L=u!=null,O=Wt(u),D=Wt(o),$=Wt(d),_=l.useCallback(()=>{if(!j.current||!R.current)return;const I={placement:t,strategy:n,middleware:x};D.current&&(I.platform=D.current),Vi(j.current,R.current,I).then(z=>{const W={...z,isPositioned:$.current!==!1};F.current&&!Rt(A.current,W)&&(A.current=W,et.flushSync(()=>{h(W)}))})},[x,t,n,D,$]);gt(()=>{d===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,h(I=>({...I,isPositioned:!1})))},[d]);const F=l.useRef(!1);gt(()=>(F.current=!0,()=>{F.current=!1}),[]),gt(()=>{if(N&&(j.current=N),E&&(R.current=E),N&&E){if(O.current)return O.current(N,E,_);_()}},[N,E,_,O,L]);const U=l.useMemo(()=>({reference:j,floating:R,setReference:b,setFloating:S}),[b,S]),T=l.useMemo(()=>({reference:N,floating:E}),[N,E]),B=l.useMemo(()=>{const I={position:n,left:0,top:0};if(!T.floating)return I;const z=$n(T.floating,p.x),W=$n(T.floating,p.y);return a?{...I,transform:"translate("+z+"px, "+W+"px)",...Ir(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:z,top:W}},[n,a,T.floating,p.x,p.y]);return l.useMemo(()=>({...p,update:_,refs:U,elements:T,floatingStyles:B}),[p,_,U,T,B])}const zi=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Bn({element:r.current,padding:o}).fn(n):{}:r?Bn({element:r,padding:o}).fn(n):{}}}},Ki=(e,t)=>({...Di(e),options:[e,t]}),qi=(e,t)=>({...Li(e),options:[e,t]}),Gi=(e,t)=>({...$i(e),options:[e,t]}),Yi=(e,t)=>({..._i(e),options:[e,t]}),Xi=(e,t)=>({...Fi(e),options:[e,t]}),Zi=(e,t)=>({...Bi(e),options:[e,t]}),Qi=(e,t)=>({...zi(e),options:[e,t]});var Ji="Arrow",Mr=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return s.jsx(H.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});Mr.displayName=Ji;var ea=Mr;function ta(e){const[t,n]=l.useState(void 0);return ee(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let c,a;if("borderBoxSize"in i){const u=i.borderBoxSize,d=Array.isArray(u)?u[0]:u;c=d.inlineSize,a=d.blockSize}else c=e.offsetWidth,a=e.offsetHeight;n({width:c,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var gn="Popper",[Or,kr]=We(gn),[na,Dr]=Or(gn),Lr=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return s.jsx(na,{scope:t,anchor:r,onAnchorChange:o,children:n})};Lr.displayName=gn;var _r="PopperAnchor",Fr=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Dr(_r,n),c=l.useRef(null),a=Q(t,c),u=l.useRef(null);return l.useEffect(()=>{const d=u.current;u.current=r?.current||c.current,d!==u.current&&i.onAnchorChange(u.current)}),r?null:s.jsx(H.div,{...o,ref:a})});Fr.displayName=_r;var xn="PopperContent",[ra,oa]=Or(xn),Br=l.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:c=0,arrowPadding:a=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:x=!1,updatePositionStrategy:m="optimized",onPlaced:g,...f}=e,v=Dr(xn,n),[w,b]=l.useState(null),S=Q(t,P=>b(P)),[N,E]=l.useState(null),j=ta(N),R=j?.width??0,A=j?.height??0,L=r+(i!=="center"?"-"+i:""),O=typeof p=="number"?p:{top:0,right:0,bottom:0,left:0,...p},D=Array.isArray(d)?d:[d],$=D.length>0,_={padding:O,boundary:D.filter(ia),altBoundary:$},{refs:F,floatingStyles:U,placement:T,isPositioned:B,middlewareData:I}=Ui({strategy:"fixed",placement:L,whileElementsMounted:(...P)=>ki(...P,{animationFrame:m==="always"}),elements:{reference:v.anchor},middleware:[Ki({mainAxis:o+A,alignmentAxis:c}),u&&qi({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?Gi():void 0,..._}),u&&Yi({..._}),Xi({..._,apply:({elements:P,rects:G,availableWidth:J,availableHeight:K})=>{const{width:q,height:Y}=G.reference,ne=P.floating.style;ne.setProperty("--radix-popper-available-width",`${J}px`),ne.setProperty("--radix-popper-available-height",`${K}px`),ne.setProperty("--radix-popper-anchor-width",`${q}px`),ne.setProperty("--radix-popper-anchor-height",`${Y}px`)}}),N&&Qi({element:N,padding:a}),aa({arrowWidth:R,arrowHeight:A}),x&&Zi({strategy:"referenceHidden",..._})]}),[z,W]=Hr(T),pe=Ae(g);ee(()=>{B&&pe?.()},[B,pe]);const te=I.arrow?.x,Ce=I.arrow?.y,ie=I.arrow?.centerOffset!==0,[ce,ae]=l.useState();return ee(()=>{w&&ae(window.getComputedStyle(w).zIndex)},[w]),s.jsx("div",{ref:F.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:B?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ce,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(ra,{scope:n,placedSide:z,onArrowChange:E,arrowX:te,arrowY:Ce,shouldHideArrow:ie,children:s.jsx(H.div,{"data-side":z,"data-align":W,...f,ref:S,style:{...f.style,animation:B?void 0:"none"}})})})});Br.displayName=xn;var $r="PopperArrow",sa={top:"bottom",right:"left",bottom:"top",left:"right"},Vr=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=oa($r,r),c=sa[i.placedSide];return s.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:s.jsx(ea,{...o,ref:n,style:{...o.style,display:"block"}})})});Vr.displayName=$r;function ia(e){return e!==null}var aa=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,c=o.arrow?.centerOffset!==0,a=c?0:e.arrowWidth,u=c?0:e.arrowHeight,[d,p]=Hr(n),h={start:"0%",center:"50%",end:"100%"}[p],x=(o.arrow?.x??0)+a/2,m=(o.arrow?.y??0)+u/2;let g="",f="";return d==="bottom"?(g=c?h:`${x}px`,f=`${-u}px`):d==="top"?(g=c?h:`${x}px`,f=`${r.floating.height+u}px`):d==="right"?(g=`${-u}px`,f=c?h:`${m}px`):d==="left"&&(g=`${r.floating.width+u}px`,f=c?h:`${m}px`),{data:{x:g,y:f}}}});function Hr(e){const[t,n="center"]=e.split("-");return[t,n]}var ca=Lr,la=Fr,ua=Br,da=Vr,fa="Portal",Wr=l.forwardRef((e,t)=>{const{container:n,...r}=e,[o,i]=l.useState(!1);ee(()=>i(!0),[]);const c=n||o&&globalThis?.document?.body;return c?Ho.createPortal(s.jsx(H.div,{...r,ref:t}),c):null});Wr.displayName=fa;function pa(e){const t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Ur=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ma="VisuallyHidden",ha=l.forwardRef((e,t)=>s.jsx(H.span,{...e,ref:t,style:{...Ur,...e.style}}));ha.displayName=ma;var va=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},_e=new WeakMap,lt=new WeakMap,ut={},Ut=0,zr=function(e){return e&&(e.host||zr(e.parentNode))},ga=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=zr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},xa=function(e,t,n,r){var o=ga(t,Array.isArray(e)?e:[e]);ut[n]||(ut[n]=new WeakMap);var i=ut[n],c=[],a=new Set,u=new Set(o),d=function(h){!h||a.has(h)||(a.add(h),d(h.parentNode))};o.forEach(d);var p=function(h){!h||u.has(h)||Array.prototype.forEach.call(h.children,function(x){if(a.has(x))p(x);else try{var m=x.getAttribute(r),g=m!==null&&m!=="false",f=(_e.get(x)||0)+1,v=(i.get(x)||0)+1;_e.set(x,f),i.set(x,v),c.push(x),f===1&&g&&lt.set(x,!0),v===1&&x.setAttribute(n,"true"),g||x.setAttribute(r,"true")}catch(w){console.error("aria-hidden: cannot operate on ",x,w)}})};return p(t),a.clear(),Ut++,function(){c.forEach(function(h){var x=_e.get(h)-1,m=i.get(h)-1;_e.set(h,x),i.set(h,m),x||(lt.has(h)||h.removeAttribute(r),lt.delete(h)),m||h.removeAttribute(n)}),Ut--,Ut||(_e=new WeakMap,_e=new WeakMap,lt=new WeakMap,ut={})}},ya=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=va(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),xa(r,o,n,"aria-hidden")):function(){return null}},ve=function(){return ve=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},ve.apply(this,arguments)};function Kr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function wa(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var xt="right-scroll-bar-position",yt="width-before-scroll-bar",ba="with-scroll-bars-hidden",Sa="--removed-body-scroll-bar-size";function zt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Ca(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Na=typeof window<"u"?l.useLayoutEffect:l.useEffect,Vn=new WeakMap;function Ea(e,t){var n=Ca(null,function(r){return e.forEach(function(o){return zt(o,r)})});return Na(function(){var r=Vn.get(n);if(r){var o=new Set(r),i=new Set(e),c=n.current;o.forEach(function(a){i.has(a)||zt(a,null)}),i.forEach(function(a){o.has(a)||zt(a,c)})}Vn.set(n,e)},[e]),n}function Ra(e){return e}function Aa(e,t){t===void 0&&(t=Ra);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var c=t(i,r);return n.push(c),function(){n=n.filter(function(a){return a!==c})}},assignSyncMedium:function(i){for(r=!0;n.length;){var c=n;n=[],c.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var c=[];if(n.length){var a=n;n=[],a.forEach(i),c=n}var u=function(){var p=c;c=[],p.forEach(i)},d=function(){return Promise.resolve().then(u)};d(),n={push:function(p){c.push(p),d()},filter:function(p){return c=c.filter(p),n}}}};return o}function Pa(e){e===void 0&&(e={});var t=Aa(null);return t.options=ve({async:!0,ssr:!1},e),t}var qr=function(e){var t=e.sideCar,n=Kr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,ve({},n))};qr.isSideCarExport=!0;function ja(e,t){return e.useMedium(t),qr}var Gr=Pa(),Kt=function(){},Mt=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:Kt,onWheelCapture:Kt,onTouchMoveCapture:Kt}),o=r[0],i=r[1],c=e.forwardProps,a=e.children,u=e.className,d=e.removeScrollBar,p=e.enabled,h=e.shards,x=e.sideCar,m=e.noRelative,g=e.noIsolation,f=e.inert,v=e.allowPinchZoom,w=e.as,b=w===void 0?"div":w,S=e.gapMode,N=Kr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=x,j=Ea([n,t]),R=ve(ve({},N),o);return l.createElement(l.Fragment,null,p&&l.createElement(E,{sideCar:Gr,removeScrollBar:d,shards:h,noRelative:m,noIsolation:g,inert:f,setCallbacks:i,allowPinchZoom:!!v,lockRef:n,gapMode:S}),c?l.cloneElement(l.Children.only(a),ve(ve({},R),{ref:j})):l.createElement(b,ve({},R,{className:u,ref:j}),a))});Mt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Mt.classNames={fullWidth:yt,zeroRight:xt};var Ta=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ia(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Ta();return t&&e.setAttribute("nonce",t),e}function Ma(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Oa(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var ka=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Ia())&&(Ma(t,n),Oa(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Da=function(){var e=ka();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Yr=function(){var e=Da(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},La={left:0,top:0,right:0,gap:0},qt=function(e){return parseInt(e||"",10)||0},_a=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[qt(n),qt(r),qt(o)]},Fa=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return La;var t=_a(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Ba=Yr(),Ve="data-scroll-locked",$a=function(e,t,n,r){var o=e.left,i=e.top,c=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(ba,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Ve,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(xt,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(yt,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(xt," .").concat(xt,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(yt," .").concat(yt,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Ve,`] {
    `).concat(Sa,": ").concat(a,`px;
  }
`)},Hn=function(){var e=parseInt(document.body.getAttribute(Ve)||"0",10);return isFinite(e)?e:0},Va=function(){l.useEffect(function(){return document.body.setAttribute(Ve,(Hn()+1).toString()),function(){var e=Hn()-1;e<=0?document.body.removeAttribute(Ve):document.body.setAttribute(Ve,e.toString())}},[])},Ha=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Va();var i=l.useMemo(function(){return Fa(o)},[o]);return l.createElement(Ba,{styles:$a(i,!t,o,n?"":"!important")})},Jt=!1;if(typeof window<"u")try{var dt=Object.defineProperty({},"passive",{get:function(){return Jt=!0,!0}});window.addEventListener("test",dt,dt),window.removeEventListener("test",dt,dt)}catch{Jt=!1}var Fe=Jt?{passive:!1}:!1,Wa=function(e){return e.tagName==="TEXTAREA"},Xr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Wa(e)&&n[t]==="visible")},Ua=function(e){return Xr(e,"overflowY")},za=function(e){return Xr(e,"overflowX")},Wn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Zr(e,r);if(o){var i=Qr(e,r),c=i[1],a=i[2];if(c>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Ka=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},qa=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Zr=function(e,t){return e==="v"?Ua(t):za(t)},Qr=function(e,t){return e==="v"?Ka(t):qa(t)},Ga=function(e,t){return e==="h"&&t==="rtl"?-1:1},Ya=function(e,t,n,r,o){var i=Ga(e,window.getComputedStyle(t).direction),c=i*r,a=n.target,u=t.contains(a),d=!1,p=c>0,h=0,x=0;do{if(!a)break;var m=Qr(e,a),g=m[0],f=m[1],v=m[2],w=f-v-i*g;(g||w)&&Zr(e,a)&&(h+=w,x+=g);var b=a.parentNode;a=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!u&&a!==document.body||u&&(t.contains(a)||t===a));return(p&&Math.abs(h)<1||!p&&Math.abs(x)<1)&&(d=!0),d},ft=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Un=function(e){return[e.deltaX,e.deltaY]},zn=function(e){return e&&"current"in e?e.current:e},Xa=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Za=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Qa=0,Be=[];function Ja(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(Qa++)[0],i=l.useState(Yr)[0],c=l.useRef(e);l.useEffect(function(){c.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var f=wa([e.lockRef.current],(e.shards||[]).map(zn),!0).filter(Boolean);return f.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),f.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=l.useCallback(function(f,v){if("touches"in f&&f.touches.length===2||f.type==="wheel"&&f.ctrlKey)return!c.current.allowPinchZoom;var w=ft(f),b=n.current,S="deltaX"in f?f.deltaX:b[0]-w[0],N="deltaY"in f?f.deltaY:b[1]-w[1],E,j=f.target,R=Math.abs(S)>Math.abs(N)?"h":"v";if("touches"in f&&R==="h"&&j.type==="range")return!1;var A=Wn(R,j);if(!A)return!0;if(A?E=R:(E=R==="v"?"h":"v",A=Wn(R,j)),!A)return!1;if(!r.current&&"changedTouches"in f&&(S||N)&&(r.current=E),!E)return!0;var L=r.current||E;return Ya(L,v,f,L==="h"?S:N)},[]),u=l.useCallback(function(f){var v=f;if(!(!Be.length||Be[Be.length-1]!==i)){var w="deltaY"in v?Un(v):ft(v),b=t.current.filter(function(E){return E.name===v.type&&(E.target===v.target||v.target===E.shadowParent)&&Xa(E.delta,w)})[0];if(b&&b.should){v.cancelable&&v.preventDefault();return}if(!b){var S=(c.current.shards||[]).map(zn).filter(Boolean).filter(function(E){return E.contains(v.target)}),N=S.length>0?a(v,S[0]):!c.current.noIsolation;N&&v.cancelable&&v.preventDefault()}}},[]),d=l.useCallback(function(f,v,w,b){var S={name:f,delta:v,target:w,should:b,shadowParent:ec(w)};t.current.push(S),setTimeout(function(){t.current=t.current.filter(function(N){return N!==S})},1)},[]),p=l.useCallback(function(f){n.current=ft(f),r.current=void 0},[]),h=l.useCallback(function(f){d(f.type,Un(f),f.target,a(f,e.lockRef.current))},[]),x=l.useCallback(function(f){d(f.type,ft(f),f.target,a(f,e.lockRef.current))},[]);l.useEffect(function(){return Be.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:x}),document.addEventListener("wheel",u,Fe),document.addEventListener("touchmove",u,Fe),document.addEventListener("touchstart",p,Fe),function(){Be=Be.filter(function(f){return f!==i}),document.removeEventListener("wheel",u,Fe),document.removeEventListener("touchmove",u,Fe),document.removeEventListener("touchstart",p,Fe)}},[]);var m=e.removeScrollBar,g=e.inert;return l.createElement(l.Fragment,null,g?l.createElement(i,{styles:Za(o)}):null,m?l.createElement(Ha,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function ec(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const tc=ja(Gr,Ja);var Jr=l.forwardRef(function(e,t){return l.createElement(Mt,ve({},e,{ref:t,sideCar:tc}))});Jr.classNames=Mt.classNames;var nc=[" ","Enter","ArrowUp","ArrowDown"],rc=[" ","Enter"],ke="Select",[Ot,kt,oc]=Gn(ke),[Ke,tl]=We(ke,[oc,kr]),Dt=kr(),[sc,Te]=Ke(ke),[ic,ac]=Ke(ke),eo=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:c,defaultValue:a,onValueChange:u,dir:d,name:p,autoComplete:h,disabled:x,required:m,form:g}=e,f=Dt(t),[v,w]=l.useState(null),[b,S]=l.useState(null),[N,E]=l.useState(!1),j=sn(d),[R,A]=bt({prop:r,defaultProp:o??!1,onChange:i,caller:ke}),[L,O]=bt({prop:c,defaultProp:a,onChange:u,caller:ke}),D=l.useRef(null),$=v?g||!!v.closest("form"):!0,[_,F]=l.useState(new Set),U=Array.from(_).map(T=>T.props.value).join(";");return s.jsx(ca,{...f,children:s.jsxs(sc,{required:m,scope:t,trigger:v,onTriggerChange:w,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:N,onValueNodeHasChildrenChange:E,contentId:Je(),value:L,onValueChange:O,open:R,onOpenChange:A,dir:j,triggerPointerDownPosRef:D,disabled:x,children:[s.jsx(Ot.Provider,{scope:t,children:s.jsx(ic,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(T=>{F(B=>new Set(B).add(T))},[]),onNativeOptionRemove:l.useCallback(T=>{F(B=>{const I=new Set(B);return I.delete(T),I})},[]),children:n})}),$?s.jsxs(Co,{"aria-hidden":!0,required:m,tabIndex:-1,name:p,autoComplete:h,value:L,onChange:T=>O(T.target.value),disabled:x,form:g,children:[L===void 0?s.jsx("option",{value:""}):null,Array.from(_)]},U):null]})})};eo.displayName=ke;var to="SelectTrigger",no=l.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,i=Dt(n),c=Te(to,n),a=c.disabled||r,u=Q(t,c.onTriggerChange),d=kt(n),p=l.useRef("touch"),[h,x,m]=Eo(f=>{const v=d().filter(S=>!S.disabled),w=v.find(S=>S.value===c.value),b=Ro(v,f,w);b!==void 0&&c.onValueChange(b.value)}),g=f=>{a||(c.onOpenChange(!0),m()),f&&(c.triggerPointerDownPosRef.current={x:Math.round(f.pageX),y:Math.round(f.pageY)})};return s.jsx(la,{asChild:!0,...i,children:s.jsx(H.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":No(c.value)?"":void 0,...o,ref:u,onClick:V(o.onClick,f=>{f.currentTarget.focus(),p.current!=="mouse"&&g(f)}),onPointerDown:V(o.onPointerDown,f=>{p.current=f.pointerType;const v=f.target;v.hasPointerCapture(f.pointerId)&&v.releasePointerCapture(f.pointerId),f.button===0&&f.ctrlKey===!1&&f.pointerType==="mouse"&&(g(f),f.preventDefault())}),onKeyDown:V(o.onKeyDown,f=>{const v=h.current!=="";!(f.ctrlKey||f.altKey||f.metaKey)&&f.key.length===1&&x(f.key),!(v&&f.key===" ")&&nc.includes(f.key)&&(g(),f.preventDefault())})})})});no.displayName=to;var ro="SelectValue",oo=l.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:c="",...a}=e,u=Te(ro,n),{onValueNodeHasChildrenChange:d}=u,p=i!==void 0,h=Q(t,u.onValueNodeChange);return ee(()=>{d(p)},[d,p]),s.jsx(H.span,{...a,ref:h,style:{pointerEvents:"none"},children:No(u.value)?s.jsx(s.Fragment,{children:c}):i})});oo.displayName=ro;var cc="SelectIcon",so=l.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return s.jsx(H.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});so.displayName=cc;var lc="SelectPortal",io=e=>s.jsx(Wr,{asChild:!0,...e});io.displayName=lc;var De="SelectContent",ao=l.forwardRef((e,t)=>{const n=Te(De,e.__scopeSelect),[r,o]=l.useState();if(ee(()=>{o(new DocumentFragment)},[]),!n.open){const i=r;return i?et.createPortal(s.jsx(co,{scope:e.__scopeSelect,children:s.jsx(Ot.Slot,{scope:e.__scopeSelect,children:s.jsx("div",{children:e.children})})}),i):null}return s.jsx(lo,{...e,ref:t})});ao.displayName=De;var le=10,[co,Ie]=Ke(De),uc="SelectContentImpl",dc=wt("SelectContent.RemoveScroll"),lo=l.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:c,side:a,sideOffset:u,align:d,alignOffset:p,arrowPadding:h,collisionBoundary:x,collisionPadding:m,sticky:g,hideWhenDetached:f,avoidCollisions:v,...w}=e,b=Te(De,n),[S,N]=l.useState(null),[E,j]=l.useState(null),R=Q(t,P=>N(P)),[A,L]=l.useState(null),[O,D]=l.useState(null),$=kt(n),[_,F]=l.useState(!1),U=l.useRef(!1);l.useEffect(()=>{if(S)return ya(S)},[S]),Ds();const T=l.useCallback(P=>{const[G,...J]=$().map(Y=>Y.ref.current),[K]=J.slice(-1),q=document.activeElement;for(const Y of P)if(Y===q||(Y?.scrollIntoView({block:"nearest"}),Y===G&&E&&(E.scrollTop=0),Y===K&&E&&(E.scrollTop=E.scrollHeight),Y?.focus(),document.activeElement!==q))return},[$,E]),B=l.useCallback(()=>T([A,S]),[T,A,S]);l.useEffect(()=>{_&&B()},[_,B]);const{onOpenChange:I,triggerPointerDownPosRef:z}=b;l.useEffect(()=>{if(S){let P={x:0,y:0};const G=K=>{P={x:Math.abs(Math.round(K.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(K.pageY)-(z.current?.y??0))}},J=K=>{P.x<=10&&P.y<=10?K.preventDefault():S.contains(K.target)||I(!1),document.removeEventListener("pointermove",G),z.current=null};return z.current!==null&&(document.addEventListener("pointermove",G),document.addEventListener("pointerup",J,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",G),document.removeEventListener("pointerup",J,{capture:!0})}}},[S,I,z]),l.useEffect(()=>{const P=()=>I(!1);return window.addEventListener("blur",P),window.addEventListener("resize",P),()=>{window.removeEventListener("blur",P),window.removeEventListener("resize",P)}},[I]);const[W,pe]=Eo(P=>{const G=$().filter(q=>!q.disabled),J=G.find(q=>q.ref.current===document.activeElement),K=Ro(G,P,J);K&&setTimeout(()=>K.ref.current.focus())}),te=l.useCallback((P,G,J)=>{const K=!U.current&&!J;(b.value!==void 0&&b.value===G||K)&&(L(P),K&&(U.current=!0))},[b.value]),Ce=l.useCallback(()=>S?.focus(),[S]),ie=l.useCallback((P,G,J)=>{const K=!U.current&&!J;(b.value!==void 0&&b.value===G||K)&&D(P)},[b.value]),ce=r==="popper"?en:uo,ae=ce===en?{side:a,sideOffset:u,align:d,alignOffset:p,arrowPadding:h,collisionBoundary:x,collisionPadding:m,sticky:g,hideWhenDetached:f,avoidCollisions:v}:{};return s.jsx(co,{scope:n,content:S,viewport:E,onViewportChange:j,itemRefCallback:te,selectedItem:A,onItemLeave:Ce,itemTextRefCallback:ie,focusSelectedItem:B,selectedItemText:O,position:r,isPositioned:_,searchRef:W,children:s.jsx(Jr,{as:dc,allowPinchZoom:!0,children:s.jsx(yr,{asChild:!0,trapped:b.open,onMountAutoFocus:P=>{P.preventDefault()},onUnmountAutoFocus:V(o,P=>{b.trigger?.focus({preventScroll:!0}),P.preventDefault()}),children:s.jsx(gr,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:c,onFocusOutside:P=>P.preventDefault(),onDismiss:()=>b.onOpenChange(!1),children:s.jsx(ce,{role:"listbox",id:b.contentId,"data-state":b.open?"open":"closed",dir:b.dir,onContextMenu:P=>P.preventDefault(),...w,...ae,onPlaced:()=>F(!0),ref:R,style:{display:"flex",flexDirection:"column",outline:"none",...w.style},onKeyDown:V(w.onKeyDown,P=>{const G=P.ctrlKey||P.altKey||P.metaKey;if(P.key==="Tab"&&P.preventDefault(),!G&&P.key.length===1&&pe(P.key),["ArrowUp","ArrowDown","Home","End"].includes(P.key)){let K=$().filter(q=>!q.disabled).map(q=>q.ref.current);if(["ArrowUp","End"].includes(P.key)&&(K=K.slice().reverse()),["ArrowUp","ArrowDown"].includes(P.key)){const q=P.target,Y=K.indexOf(q);K=K.slice(Y+1)}setTimeout(()=>T(K)),P.preventDefault()}})})})})})})});lo.displayName=uc;var fc="SelectItemAlignedPosition",uo=l.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,i=Te(De,n),c=Ie(De,n),[a,u]=l.useState(null),[d,p]=l.useState(null),h=Q(t,R=>p(R)),x=kt(n),m=l.useRef(!1),g=l.useRef(!0),{viewport:f,selectedItem:v,selectedItemText:w,focusSelectedItem:b}=c,S=l.useCallback(()=>{if(i.trigger&&i.valueNode&&a&&d&&f&&v&&w){const R=i.trigger.getBoundingClientRect(),A=d.getBoundingClientRect(),L=i.valueNode.getBoundingClientRect(),O=w.getBoundingClientRect();if(i.dir!=="rtl"){const q=O.left-A.left,Y=L.left-q,ne=R.left-Y,Z=R.width+ne,Le=Math.max(Z,A.width),y=window.innerWidth-le,C=Cn(Y,[le,Math.max(le,y-Le)]);a.style.minWidth=Z+"px",a.style.left=C+"px"}else{const q=A.right-O.right,Y=window.innerWidth-L.right-q,ne=window.innerWidth-R.right-Y,Z=R.width+ne,Le=Math.max(Z,A.width),y=window.innerWidth-le,C=Cn(Y,[le,Math.max(le,y-Le)]);a.style.minWidth=Z+"px",a.style.right=C+"px"}const D=x(),$=window.innerHeight-le*2,_=f.scrollHeight,F=window.getComputedStyle(d),U=parseInt(F.borderTopWidth,10),T=parseInt(F.paddingTop,10),B=parseInt(F.borderBottomWidth,10),I=parseInt(F.paddingBottom,10),z=U+T+_+I+B,W=Math.min(v.offsetHeight*5,z),pe=window.getComputedStyle(f),te=parseInt(pe.paddingTop,10),Ce=parseInt(pe.paddingBottom,10),ie=R.top+R.height/2-le,ce=$-ie,ae=v.offsetHeight/2,P=v.offsetTop+ae,G=U+T+P,J=z-G;if(G<=ie){const q=D.length>0&&v===D[D.length-1].ref.current;a.style.bottom="0px";const Y=d.clientHeight-f.offsetTop-f.offsetHeight,ne=Math.max(ce,ae+(q?Ce:0)+Y+B),Z=G+ne;a.style.height=Z+"px"}else{const q=D.length>0&&v===D[0].ref.current;a.style.top="0px";const ne=Math.max(ie,U+f.offsetTop+(q?te:0)+ae)+J;a.style.height=ne+"px",f.scrollTop=G-ie+f.offsetTop}a.style.margin=`${le}px 0`,a.style.minHeight=W+"px",a.style.maxHeight=$+"px",r?.(),requestAnimationFrame(()=>m.current=!0)}},[x,i.trigger,i.valueNode,a,d,f,v,w,i.dir,r]);ee(()=>S(),[S]);const[N,E]=l.useState();ee(()=>{d&&E(window.getComputedStyle(d).zIndex)},[d]);const j=l.useCallback(R=>{R&&g.current===!0&&(S(),b?.(),g.current=!1)},[S,b]);return s.jsx(mc,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:m,onScrollButtonChange:j,children:s.jsx("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:s.jsx(H.div,{...o,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});uo.displayName=fc;var pc="SelectPopperPosition",en=l.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=le,...i}=e,c=Dt(n);return s.jsx(ua,{...c,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});en.displayName=pc;var[mc,yn]=Ke(De,{}),tn="SelectViewport",fo=l.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,i=Ie(tn,n),c=yn(tn,n),a=Q(t,i.onViewportChange),u=l.useRef(0);return s.jsxs(s.Fragment,{children:[s.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),s.jsx(Ot.Slot,{scope:n,children:s.jsx(H.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:V(o.onScroll,d=>{const p=d.currentTarget,{contentWrapper:h,shouldExpandOnScrollRef:x}=c;if(x?.current&&h){const m=Math.abs(u.current-p.scrollTop);if(m>0){const g=window.innerHeight-le*2,f=parseFloat(h.style.minHeight),v=parseFloat(h.style.height),w=Math.max(f,v);if(w<g){const b=w+m,S=Math.min(g,b),N=b-S;h.style.height=S+"px",h.style.bottom==="0px"&&(p.scrollTop=N>0?N:0,h.style.justifyContent="flex-end")}}}u.current=p.scrollTop})})})]})});fo.displayName=tn;var po="SelectGroup",[hc,vc]=Ke(po),gc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Je();return s.jsx(hc,{scope:n,id:o,children:s.jsx(H.div,{role:"group","aria-labelledby":o,...r,ref:t})})});gc.displayName=po;var mo="SelectLabel",xc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=vc(mo,n);return s.jsx(H.div,{id:o.id,...r,ref:t})});xc.displayName=mo;var At="SelectItem",[yc,ho]=Ke(At),vo=l.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...c}=e,a=Te(At,n),u=Ie(At,n),d=a.value===r,[p,h]=l.useState(i??""),[x,m]=l.useState(!1),g=Q(t,b=>u.itemRefCallback?.(b,r,o)),f=Je(),v=l.useRef("touch"),w=()=>{o||(a.onValueChange(r),a.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return s.jsx(yc,{scope:n,value:r,disabled:o,textId:f,isSelected:d,onItemTextChange:l.useCallback(b=>{h(S=>S||(b?.textContent??"").trim())},[]),children:s.jsx(Ot.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:s.jsx(H.div,{role:"option","aria-labelledby":f,"data-highlighted":x?"":void 0,"aria-selected":d&&x,"data-state":d?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...c,ref:g,onFocus:V(c.onFocus,()=>m(!0)),onBlur:V(c.onBlur,()=>m(!1)),onClick:V(c.onClick,()=>{v.current!=="mouse"&&w()}),onPointerUp:V(c.onPointerUp,()=>{v.current==="mouse"&&w()}),onPointerDown:V(c.onPointerDown,b=>{v.current=b.pointerType}),onPointerMove:V(c.onPointerMove,b=>{v.current=b.pointerType,o?u.onItemLeave?.():v.current==="mouse"&&b.currentTarget.focus({preventScroll:!0})}),onPointerLeave:V(c.onPointerLeave,b=>{b.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:V(c.onKeyDown,b=>{u.searchRef?.current!==""&&b.key===" "||(rc.includes(b.key)&&w(),b.key===" "&&b.preventDefault())})})})})});vo.displayName=At;var Xe="SelectItemText",go=l.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...i}=e,c=Te(Xe,n),a=Ie(Xe,n),u=ho(Xe,n),d=ac(Xe,n),[p,h]=l.useState(null),x=Q(t,w=>h(w),u.onItemTextChange,w=>a.itemTextRefCallback?.(w,u.value,u.disabled)),m=p?.textContent,g=l.useMemo(()=>s.jsx("option",{value:u.value,disabled:u.disabled,children:m},u.value),[u.disabled,u.value,m]),{onNativeOptionAdd:f,onNativeOptionRemove:v}=d;return ee(()=>(f(g),()=>v(g)),[f,v,g]),s.jsxs(s.Fragment,{children:[s.jsx(H.span,{id:u.textId,...i,ref:x}),u.isSelected&&c.valueNode&&!c.valueNodeHasChildren?et.createPortal(i.children,c.valueNode):null]})});go.displayName=Xe;var xo="SelectItemIndicator",yo=l.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return ho(xo,n).isSelected?s.jsx(H.span,{"aria-hidden":!0,...r,ref:t}):null});yo.displayName=xo;var nn="SelectScrollUpButton",wo=l.forwardRef((e,t)=>{const n=Ie(nn,e.__scopeSelect),r=yn(nn,e.__scopeSelect),[o,i]=l.useState(!1),c=Q(t,r.onScrollButtonChange);return ee(()=>{if(n.viewport&&n.isPositioned){let a=function(){const d=u.scrollTop>0;i(d)};const u=n.viewport;return a(),u.addEventListener("scroll",a),()=>u.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),o?s.jsx(So,{...e,ref:c,onAutoScroll:()=>{const{viewport:a,selectedItem:u}=n;a&&u&&(a.scrollTop=a.scrollTop-u.offsetHeight)}}):null});wo.displayName=nn;var rn="SelectScrollDownButton",bo=l.forwardRef((e,t)=>{const n=Ie(rn,e.__scopeSelect),r=yn(rn,e.__scopeSelect),[o,i]=l.useState(!1),c=Q(t,r.onScrollButtonChange);return ee(()=>{if(n.viewport&&n.isPositioned){let a=function(){const d=u.scrollHeight-u.clientHeight,p=Math.ceil(u.scrollTop)<d;i(p)};const u=n.viewport;return a(),u.addEventListener("scroll",a),()=>u.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),o?s.jsx(So,{...e,ref:c,onAutoScroll:()=>{const{viewport:a,selectedItem:u}=n;a&&u&&(a.scrollTop=a.scrollTop+u.offsetHeight)}}):null});bo.displayName=rn;var So=l.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,i=Ie("SelectScrollButton",n),c=l.useRef(null),a=kt(n),u=l.useCallback(()=>{c.current!==null&&(window.clearInterval(c.current),c.current=null)},[]);return l.useEffect(()=>()=>u(),[u]),ee(()=>{a().find(p=>p.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[a]),s.jsx(H.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:V(o.onPointerDown,()=>{c.current===null&&(c.current=window.setInterval(r,50))}),onPointerMove:V(o.onPointerMove,()=>{i.onItemLeave?.(),c.current===null&&(c.current=window.setInterval(r,50))}),onPointerLeave:V(o.onPointerLeave,()=>{u()})})}),wc="SelectSeparator",bc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return s.jsx(H.div,{"aria-hidden":!0,...r,ref:t})});bc.displayName=wc;var on="SelectArrow",Sc=l.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Dt(n),i=Te(on,n),c=Ie(on,n);return i.open&&c.position==="popper"?s.jsx(da,{...o,...r,ref:t}):null});Sc.displayName=on;var Cc="SelectBubbleInput",Co=l.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{const o=l.useRef(null),i=Q(r,o),c=pa(t);return l.useEffect(()=>{const a=o.current;if(!a)return;const u=window.HTMLSelectElement.prototype,p=Object.getOwnPropertyDescriptor(u,"value").set;if(c!==t&&p){const h=new Event("change",{bubbles:!0});p.call(a,t),a.dispatchEvent(h)}},[c,t]),s.jsx(H.select,{...n,style:{...Ur,...n.style},ref:i,defaultValue:t})});Co.displayName=Cc;function No(e){return e===""||e===void 0}function Eo(e){const t=Ae(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(c=>{const a=n.current+c;t(a),(function u(d){n.current=d,window.clearTimeout(r.current),d!==""&&(r.current=window.setTimeout(()=>u(""),1e3))})(a)},[t]),i=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function Ro(e,t,n){const o=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let c=Nc(e,Math.max(i,0));o.length===1&&(c=c.filter(d=>d!==n));const u=c.find(d=>d.textValue.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}function Nc(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Ec=eo,Rc=no,Ac=oo,Pc=so,jc=io,Tc=ao,Ic=fo,Mc=vo,Oc=go,kc=yo,Dc=wo,Lc=bo;/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _c=fe("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=fe("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bc=fe("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ao=fe("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Po=fe("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=fe("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vc=fe("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hc=fe("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wc=fe("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=fe("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=fe("SquareCheckBig",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);function pt({...e}){return s.jsx(Ec,{"data-slot":"select",...e})}function mt({...e}){return s.jsx(Ac,{"data-slot":"select-value",...e})}function ht({className:e,size:t="default",children:n,...r}){return s.jsxs(Rc,{"data-slot":"select-trigger","data-size":t,className:X("border-gray-300 dark:border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-foreground dark:[&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 hover:border-gray-400 dark:hover:border-gray-500 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow,border-color] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[n,s.jsx(Pc,{asChild:!0,children:s.jsx(Ao,{className:"size-4 opacity-70"})})]})}function vt({className:e,children:t,position:n="popper",...r}){return s.jsx(jc,{children:s.jsxs(Tc,{"data-slot":"select-content",className:X("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[s.jsx(Kc,{}),s.jsx(Ic,{className:X("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),s.jsx(qc,{})]})})}function Me({className:e,children:t,...n}){return s.jsxs(Mc,{"data-slot":"select-item",className:X("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[s.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:s.jsx(kc,{children:s.jsx(Bc,{className:"size-4"})})}),s.jsx(Oc,{children:t})]})}function Kc({className:e,...t}){return s.jsx(Dc,{"data-slot":"select-scroll-up-button",className:X("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(Po,{className:"size-4"})})}function qc({className:e,...t}){return s.jsx(Lc,{"data-slot":"select-scroll-down-button",className:X("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(Ao,{className:"size-4"})})}class Gc{constructor(){_t(this,"api",null);_t(this,"messageListeners",new Map);window.acquireVsCodeApi&&(this.api=window.acquireVsCodeApi()),window.addEventListener("message",t=>{const n=t.data;this.notifyListeners(n.type,n)})}postMessage(t){this.api?this.api.postMessage(t):console.warn("VSCode API not available, message not sent:",t)}onMessage(t,n){return this.messageListeners.has(t)||this.messageListeners.set(t,new Set),this.messageListeners.get(t).add(n),()=>{this.messageListeners.get(t)?.delete(n)}}notifyListeners(t,n){const r=this.messageListeners.get(t);r&&r.forEach(o=>o(n))}setState(t){this.api&&this.api.setState(t)}getState(){return this.api?this.api.getState():null}refreshAll(){this.postMessage({type:"refresh-all"})}getSpecs(){this.postMessage({type:"get-specs"})}getTasks(t){this.postMessage({type:"get-tasks",specName:t})}updateTaskStatus(t,n,r){this.postMessage({type:"update-task-status",specName:t,taskId:n,status:r})}saveDocument(t,n,r){this.postMessage({type:"save-document",specName:t,docType:n,content:r})}getApprovals(){this.postMessage({type:"get-approvals"})}getApprovalCategories(){this.postMessage({type:"get-approval-categories"})}approveRequest(t,n){this.postMessage({type:"approve-request",id:t,response:n})}rejectRequest(t,n){this.postMessage({type:"reject-request",id:t,response:n})}requestRevisionRequest(t,n,r,o){this.postMessage({type:"request-revision-request",id:t,response:n,annotations:r,comments:o})}getApprovalContent(t){this.postMessage({type:"get-approval-content",id:t})}getSteering(){this.postMessage({type:"get-steering"})}getSpecDocuments(t){this.postMessage({type:"get-spec-documents",specName:t})}getSteeringDocuments(){this.postMessage({type:"get-steering-documents"})}openDocument(t,n){this.postMessage({type:"open-document",specName:t,docType:n})}openSteeringDocument(t){this.postMessage({type:"open-steering-document",docType:t})}getSelectedSpec(){this.postMessage({type:"get-selected-spec"})}setSelectedSpec(t){this.postMessage({type:"set-selected-spec",specName:t})}getConfig(){this.postMessage({type:"get-config"})}getArchivedSpecs(){this.postMessage({type:"get-archived-specs"})}archiveSpec(t){this.postMessage({type:"archive-spec",specName:t})}unarchiveSpec(t){this.postMessage({type:"unarchive-spec",specName:t})}openExternalUrl(t){this.postMessage({type:"open-external-url",url:t})}}const k=new Gc;function Yc(e={}){const{enabled:t=!0,volume:n=.3,soundUris:r=null}=e,o=l.useRef(new Map),i=l.useRef(!1);l.useEffect(()=>{const m=()=>{i.current=!0,console.log("User interaction detected - audio playback now allowed"),document.removeEventListener("click",m),document.removeEventListener("keydown",m),document.removeEventListener("touchstart",m)};return document.addEventListener("click",m,{passive:!0}),document.addEventListener("keydown",m,{passive:!0}),document.addEventListener("touchstart",m,{passive:!0}),()=>{document.removeEventListener("click",m),document.removeEventListener("keydown",m),document.removeEventListener("touchstart",m)}},[]);const c=l.useCallback(m=>{if(r&&r[m])return console.log("Found sound URI via PostMessage:",r[m]),r[m];const g=window.soundURIs;if(g&&g[m])return console.log("Found sound URI via window.soundURIs:",g[m]),g[m];const f=document.getElementById("root");if(f){const v=m==="approval-pending"?f.getAttribute("data-approval-sound-uri"):f.getAttribute("data-task-completed-sound-uri");if(v)return console.log("Found sound URI via data attribute:",v),v}return console.warn(`Sound URI not found for: ${m}`,{postMessageUris:r,windowSoundURIs:g,requestedSound:m,soundURIsReady:window.soundURIsReady}),null},[r]),a=l.useCallback(m=>{if(!t)return null;let g=o.current.get(m);if(!g)try{g=new Audio;const f=c(m);if(!f)return null;g.src=f,console.log("Set audio source to:",g.src),g.volume=n,g.preload="auto",g.addEventListener("error",v=>{console.warn(`Failed to load sound: ${m}`,v),o.current.delete(m)}),o.current.set(m,g)}catch(f){return console.warn(`Failed to create audio element for ${m}:`,f),null}return g&&(g.volume=n),g},[t,n,c]),u=l.useCallback(async m=>{if(console.log("playNotification called with:",{sound:m,enabled:t,userHasInteracted:i.current}),!t){console.log("Sound notifications disabled");return}if(!i.current){console.warn("Cannot play sound - user has not interacted with page yet (browser autoplay restriction)");return}try{const g=a(m);if(!g){console.warn("No audio element available for sound:",m);return}console.log("Playing sound:",m,"with audio element:",g),g.currentTime=0;const f=g.play();f?(console.log("Awaiting audio playback..."),await f,console.log("Audio playback completed successfully")):console.log("Audio play() did not return promise (legacy browser)")}catch(g){console.warn(`Sound notification blocked or failed for ${m}:`,g)}},[t,a]),d=l.useCallback(()=>u("approval-pending"),[u]),p=l.useCallback(()=>u("task-completed"),[u]),h=l.useCallback(m=>u(m),[u]),x=l.useCallback(()=>{o.current.forEach(m=>{m.pause(),m.removeAttribute("src"),m.load()}),o.current.clear()},[]);return{playApprovalPending:d,playTaskCompleted:p,testSound:h,cleanup:x,isEnabled:t}}function Xc(){console.log("=== WEBVIEW APP.TSX STARTING ===");const e=_o();console.log("Current VS Code theme:",e);const[t,n]=l.useState([]),[r,o]=l.useState([]),[i,c]=l.useState(null),[a,u]=l.useState(null),[d,p]=l.useState([]),[h,x]=l.useState([]),[m,g]=l.useState("all"),[f,v]=l.useState([]),[w,b]=l.useState([]),[,S]=l.useState(null),[N,E]=l.useState(!1),[j,R]=l.useState("overview"),[A,L]=l.useState(null),[O,D]=l.useState(null),[$,_]=l.useState(null),[F,U]=l.useState(!1),[T,B]=l.useState({enabled:!0,volume:.3,approvalSound:!0,taskCompletionSound:!0}),[I,z]=l.useState(null),[W,pe]=l.useState("active"),[te,Ce]=l.useState(null),ie=he.useRef(null),ce=Yc({enabled:T.enabled,volume:T.volume,soundUris:I}),ae=l.useRef([]),P=l.useRef(null),G=y=>{if(!i)return;const C=`Please work on task ${y} for spec "${i}"`;navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(C).then(()=>{_(y),setTimeout(()=>_(null),2e3)}).catch(()=>{J(C,y)}):J(C,y)},J=(y,C)=>{const M=document.createElement("textarea");M.value=y,M.style.position="fixed",M.style.left="-999999px",M.style.top="-999999px",document.body.appendChild(M),M.focus(),M.select();try{document.execCommand("copy")&&(_(C),setTimeout(()=>_(null),2e3))}catch(qe){console.error("Failed to copy text: ",qe)}document.body.removeChild(M)},K=()=>{ie.current?.scrollTo({top:0,behavior:"smooth"})};l.useEffect(()=>{const y=[k.onMessage("specs-updated",C=>{n(C.data||[]),E(!1)}),k.onMessage("tasks-updated",C=>{console.log("=== App.tsx tasks-updated message ==="),console.log("Message data:",C.data),console.log("Selected spec:",i),console.log("Message spec:",C.data?.specName),C.data&&(console.log("Setting taskData with taskList count:",C.data.taskList?.length),console.log("Sample task (2.2) from message:",C.data.taskList?.find(M=>M.id==="2.2")),console.log("Tasks with metadata:",C.data.taskList?.filter(M=>M.requirements?.length||M.implementationDetails?.length||M.files?.length||M.purposes?.length||M.leverage).map(M=>({id:M.id,requirements:M.requirements,implementationDetails:M.implementationDetails}))),u(C.data),!i&&C.data.specName&&(console.log("Setting selected spec from task data:",C.data.specName),c(C.data.specName)))}),k.onMessage("approvals-updated",C=>{console.log("=== Received approvals-updated message ==="),console.log("Current tab:",j),console.log("Approvals count:",C.data?.length||0),console.log("Pending approvals:",C.data?.filter(M=>M.status==="pending").length||0),console.log("About to setApprovals - this should trigger badge counter update"),p(C.data||[]),k.getApprovalCategories()}),k.onMessage("approval-categories-updated",C=>{console.log("=== Received approval-categories-updated message ==="),console.log("Categories:",C.data),x(C.data||[])}),k.onMessage("steering-updated",C=>{S(C.data)}),k.onMessage("spec-documents-updated",C=>{v(C.data||[])}),k.onMessage("steering-documents-updated",C=>{b(C.data||[])}),k.onMessage("selected-spec-updated",C=>{c(C.data||null)}),k.onMessage("error",C=>{console.error("Extension error:",C.message),E(!1)}),k.onMessage("notification",C=>{L({message:C.message,level:C.level}),setTimeout(()=>L(null),3e3)}),k.onMessage("config-updated",C=>{B(C.data||{enabled:!0,volume:.3,approvalSound:!0,taskCompletionSound:!0})}),k.onMessage("sound-uris-updated",C=>{console.log("Received sound URIs from extension:",C.data),z(C.data||null)}),k.onMessage("navigate-to-approvals",C=>{console.log("Navigating to approvals from native notification:",C.data);const{specName:M,approvalId:qe}=C.data;R("approvals"),c(M),console.log("Switched to approvals tab, selected spec:",M)}),k.onMessage("archived-specs-updated",C=>{console.log("=== Received archived-specs-updated message ==="),console.log("Archived specs count:",C.data?.length||0),o(C.data||[])})];return q(),k.getApprovals(),()=>{y.forEach(C=>C())}},[]),l.useEffect(()=>{i&&(k.getTasks(i),k.getSpecDocuments(i))},[i]),l.useEffect(()=>{te&&k.getSpecDocuments(te)},[te]),l.useEffect(()=>{k.getSteeringDocuments()},[]),l.useEffect(()=>{const y=ie.current;if(!y)return;const C=()=>{U(y.scrollTop>200)};return y.addEventListener("scroll",C),()=>y.removeEventListener("scroll",C)},[]),l.useEffect(()=>{if(d.length===0){ae.current=d;return}const y=d.filter(M=>M.status==="pending").length,C=ae.current.filter(M=>M.status==="pending").length;y>C&&ae.current.length>0&&T.approvalSound&&(console.log(`New pending approval detected: ${y} vs ${C}`),ce.playApprovalPending()),ae.current=d},[d,ce,T.approvalSound]),l.useEffect(()=>{if(!a||!a.taskList){P.current=a;return}if(!P.current||!P.current.taskList){P.current=a;return}const y=a.taskList.filter(M=>M.status==="completed").length,C=P.current.taskList.filter(M=>M.status==="completed").length;y>C&&T.taskCompletionSound&&(console.log(`Task completion detected: ${y} vs ${C}`),ce.playTaskCompleted()),P.current=a},[a,ce,T.taskCompletionSound]),l.useEffect(()=>{j==="approvals"?(k.getApprovals(),k.getApprovalCategories()):j==="archives"&&k.getArchivedSpecs()},[j]);const q=()=>{E(!0),k.refreshAll(),k.getSelectedSpec(),k.getConfig(),k.getArchivedSpecs()},Y=y=>{k.setSelectedSpec(y)},ne=(y,C)=>{i&&k.updateTaskStatus(i,y,C)},Z=he.useMemo(()=>{const y=t.filter(me=>!me.isArchived).length,C=r.length,M=y+C,qe=t.filter(me=>me.taskProgress&&me.taskProgress.completed===me.taskProgress.total&&me.taskProgress.total>0).length,jo=t.reduce((me,Lt)=>me+(Lt.taskProgress?.total||0),0),To=t.reduce((me,Lt)=>me+(Lt.taskProgress?.completed||0),0);return{activeSpecs:y,archivedSpecs:C,totalSpecs:M,completedSpecs:qe,totalTasks:jo,completedTasks:To}},[t,r]),Le=he.useMemo(()=>{const y=d.filter(C=>C.status==="pending").length;return console.log("Badge counter recalculated:",y,"from",d.length,"total approvals"),y},[d]);return s.jsx("div",{className:X("sidebar-root",`vscode-${e}`),children:s.jsxs(vs,{value:j,onValueChange:R,className:"w-full h-full flex flex-col",children:[s.jsxs("div",{className:"sidebar-sticky-header space-y-3",children:[A&&s.jsx("div",{className:X("p-2 rounded text-xs font-medium",A.level==="success"&&"bg-green-100 text-green-800 border border-green-200",A.level==="error"&&"bg-red-100 text-red-800 border border-red-200",A.level==="warning"&&"bg-yellow-100 text-yellow-800 border border-yellow-200",A.level==="info"&&"bg-blue-100 text-blue-800 border border-blue-200"),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{children:A.message}),s.jsx("button",{type:"button",onClick:()=>L(null),className:"ml-2 hover:opacity-70",children:"×"})]})}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h1",{className:"text-lg font-semibold",children:"Spec Workflow MCP"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(re,{variant:"ghost",size:"sm",onClick:()=>k.openExternalUrl("https://buymeacoffee.com/pimzino"),title:"Support this project",className:"text-orange-600 hover:text-orange-700 hover:bg-orange-50",children:s.jsx(Vc,{className:"h-4 w-4"})}),s.jsx(re,{variant:"ghost",size:"sm",onClick:q,disabled:N,children:s.jsx(Wc,{className:X("h-4 w-4",N&&"animate-spin")})})]})]}),s.jsxs(gs,{className:"grid w-full grid-cols-5",children:[s.jsx(Ge,{value:"overview",className:"text-xs",title:"Project Overview",children:s.jsx(_c,{className:"h-3 w-3"})}),s.jsx(Ge,{value:"steering",className:"text-xs",title:"Steering Documents",children:s.jsx(Uc,{className:"h-3 w-3"})}),s.jsx(Ge,{value:"specs",className:"text-xs",title:"Specification Documents",children:s.jsx(Fc,{className:"h-3 w-3"})}),s.jsx(Ge,{value:"tasks",className:"text-xs",title:"Task Management",children:s.jsx(zc,{className:"h-3 w-3"})}),s.jsxs(Ge,{value:"approvals",className:"text-xs relative",title:"Approval Requests",children:[s.jsx($c,{className:"h-3 w-3"}),Le>0&&s.jsx(at,{variant:"destructive",className:"absolute -top-1 -right-1 h-4 w-4 p-0 text-xs flex items-center justify-center rounded-full min-w-[16px]",children:Le})]})]})]}),s.jsxs("div",{className:"sidebar-scrollable-content",ref:ie,children:[s.jsxs(Ye,{value:"overview",className:"space-y-3",children:[s.jsxs(Ne,{children:[s.jsx(rt,{className:"pb-2",children:s.jsx(ot,{className:"text-sm",children:"Project Overview"})}),s.jsxs(Ee,{className:"space-y-3",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-3 text-xs",children:[s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-muted-foreground",children:"Active Specs"}),s.jsxs("div",{className:"font-medium",children:[Z.completedSpecs," / ",Z.activeSpecs]})]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-muted-foreground",children:"Archived Specs"}),s.jsx("div",{className:"font-medium",children:Z.archivedSpecs})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-3 text-xs",children:[s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-muted-foreground",children:"Total Specs"}),s.jsx("div",{className:"font-medium",children:Z.totalSpecs})]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-muted-foreground",children:"Tasks"}),s.jsxs("div",{className:"font-medium",children:[Z.completedTasks," / ",Z.totalTasks]})]})]}),Z.totalTasks>0&&s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-xs",children:[s.jsx("span",{children:"Overall Progress"}),s.jsxs("span",{children:[Math.round(Z.completedTasks/Z.totalTasks*100),"%"]})]}),s.jsx(Sn,{value:Z.completedTasks/Z.totalTasks*100,className:"h-2"})]})]})]}),s.jsxs(Ne,{children:[s.jsx(rt,{className:"pb-2",children:s.jsx(ot,{className:"text-sm",children:"Recent Activity"})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-2",children:[t.slice(0,3).map(y=>s.jsxs("div",{className:"flex items-center justify-between text-xs",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:X("w-2 h-2 rounded-full",y.taskProgress&&y.taskProgress.completed===y.taskProgress.total&&y.taskProgress.total>0?"bg-green-500":"bg-blue-500")}),s.jsx("span",{className:"truncate",children:y.displayName})]}),s.jsx("span",{className:"text-muted-foreground",children:st(y.lastModified)})]},y.name)),t.length===0&&s.jsx("div",{className:"text-muted-foreground text-xs text-center py-2",children:"No specs found"})]})})]})]}),s.jsxs(Ye,{value:"tasks",className:"space-y-3",children:[s.jsx("div",{className:"space-y-3",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"Specification:"}),s.jsxs(pt,{value:i||"",onValueChange:Y,children:[s.jsx(ht,{className:"w-full",children:s.jsx(mt,{placeholder:"Select a specification"})}),s.jsx(vt,{children:t.map(y=>s.jsx(Me,{value:y.name,children:y.displayName},y.name))})]})]})}),i?a?s.jsxs(s.Fragment,{children:[s.jsx(Ne,{children:s.jsx(Ee,{className:"p-4",children:s.jsxs("div",{className:"grid grid-cols-4 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"font-medium text-lg",children:a.total}),s.jsx("div",{className:"text-muted-foreground text-xs",children:"Total"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"font-medium text-lg text-green-600",children:a.completed}),s.jsx("div",{className:"text-muted-foreground text-xs",children:"Done"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"font-medium text-lg text-amber-600",children:a.total-a.completed}),s.jsx("div",{className:"text-muted-foreground text-xs",children:"Left"})]}),s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"font-medium text-lg text-blue-600",children:[Math.round(a.progress),"%"]}),s.jsx("div",{className:"text-muted-foreground text-xs",children:"Progress"})]})]})})}),s.jsx(Ne,{children:s.jsxs(Ee,{className:"p-3",children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("span",{className:"text-sm font-medium",children:"Overall Progress"}),s.jsxs("span",{className:"text-sm",children:[Math.round(a.progress),"%"]})]}),s.jsx(Sn,{value:a.progress,className:"h-2"})]})}),s.jsx("div",{className:"space-y-2",children:a.taskList?.map(y=>(console.log(`🔍 TASK DEBUG [${y.id}]:`,{id:y.id,status:y.status,completed:y.completed,inProgress:y.inProgress,hasInProgress:"inProgress"in y,allProps:Object.keys(y)}),s.jsx(Ne,{className:X("transition-colors",y.isHeader&&"border-purple-200 dark:border-slate-600 bg-purple-50 dark:bg-slate-800/60",y.status==="in-progress"&&"border-orange-500",y.completed&&"border-green-500"),children:s.jsx(Ee,{className:"p-3",children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("span",{className:X("text-sm flex-1",y.isHeader?"font-semibold text-purple-900 dark:text-purple-100":"font-medium"),children:[y.isHeader?"Section":"Task"," ",y.id]}),!y.isHeader&&s.jsxs(s.Fragment,{children:[s.jsx(re,{variant:"ghost",size:"sm",className:X("h-6 w-6 p-0",$===y.id&&"text-green-600"),onClick:C=>{C.stopPropagation(),G(y.id)},title:$===y.id?"Copied!":"Copy prompt for AI agent",disabled:$===y.id,children:s.jsx(Hc,{className:"h-3 w-3"})}),s.jsxs(pt,{value:y.completed?"completed":y.status||"pending",onValueChange:C=>ne(y.id,C),children:[s.jsx(ht,{className:X("w-auto h-6 px-2 text-xs border-0 focus:ring-0 focus:ring-offset-0",y.completed?"bg-green-500 text-white [&_svg]:!text-white [&_svg]:opacity-100":y.status==="in-progress"?"bg-orange-500 text-white [&_svg]:!text-white [&_svg]:opacity-100":"bg-transparent border border-border text-foreground [&_svg]:text-foreground"),children:s.jsx(mt,{})}),s.jsxs(vt,{children:[s.jsx(Me,{value:"pending",children:"Pending"}),s.jsx(Me,{value:"in-progress",children:"In Progress"}),s.jsx(Me,{value:"completed",children:"Completed"})]})]})]}),y.isHeader&&s.jsx(at,{variant:"secondary",className:"text-xs bg-purple-100 dark:bg-slate-700 text-purple-700 dark:text-slate-200 border-purple-300 dark:border-slate-500",children:"Task Group"})]}),s.jsx("p",{className:X("text-xs",y.isHeader?"text-slate-600 dark:text-slate-300":"text-muted-foreground"),children:y.description}),s.jsxs("div",{className:"space-y-2 border-t border-gray-100 dark:border-gray-700 pt-2",children:[y.files&&y.files.length>0&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs font-medium text-purple-600 dark:text-purple-400 flex items-center gap-1",children:"Files:"}),s.jsx("div",{className:"task-files-container",children:s.jsx("div",{className:"task-files-list",children:y.files.map((C,M)=>s.jsx("span",{className:"px-2 py-1 bg-purple-50 dark:bg-purple-950/30 text-purple-700 dark:text-purple-300 text-xs rounded border border-purple-200 dark:border-purple-800 font-mono whitespace-nowrap flex-shrink-0",children:C},M))})})]}),y.implementationDetails&&y.implementationDetails.length>0&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs font-medium text-blue-600 dark:text-blue-400 flex items-center gap-1",children:"Implementation:"}),s.jsx("ul",{className:"text-xs text-muted-foreground list-disc list-inside space-y-0.5 ml-2",children:y.implementationDetails.map((C,M)=>s.jsx("li",{className:"leading-relaxed",children:C},M))})]}),y.purposes&&y.purposes.length>0&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs font-medium text-green-600 dark:text-green-400 flex items-center gap-1",children:"Purposes:"}),s.jsx("ul",{className:"text-xs text-muted-foreground list-disc list-inside space-y-0.5 ml-2",children:y.purposes.map((C,M)=>s.jsx("li",{className:"leading-relaxed",children:C},M))})]}),y.requirements&&y.requirements.length>0&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs font-medium text-orange-600 dark:text-orange-400 flex items-center gap-1",children:"Requirements:"}),s.jsx("div",{className:"text-xs text-muted-foreground",children:y.requirements.join(", ")})]}),y.leverage&&s.jsxs("div",{className:"space-y-1",children:[s.jsx("div",{className:"text-xs font-medium text-cyan-600 dark:text-cyan-400 flex items-center gap-1",children:"Leverage:"}),s.jsx("div",{className:"text-xs text-muted-foreground bg-cyan-50 dark:bg-cyan-950/30 border border-cyan-200 dark:border-cyan-800 rounded px-2 py-1 font-mono",children:y.leverage})]})]})]})})},y.id)))})]}):s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:"Loading tasks..."}):s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:t.length===0?"No specifications found":"Select a specification above to view tasks"})]}),s.jsxs(Ye,{value:"approvals",className:"space-y-3",children:[s.jsx("div",{className:"space-y-3",children:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"Document:"}),s.jsxs(pt,{value:m,onValueChange:g,children:[s.jsx(ht,{className:"w-full",children:s.jsx(mt,{placeholder:"Select a category"})}),s.jsx(vt,{children:h.map(y=>s.jsx(Me,{value:y.value,children:s.jsxs("div",{className:"flex items-center justify-between w-full",children:[s.jsx("span",{children:y.label}),y.count>0&&s.jsx(at,{variant:"secondary",className:"ml-2 h-4 w-4 p-0 text-xs flex items-center justify-center rounded-full min-w-[16px]",children:y.count})]})},y.value))})]})]})}),m?(()=>{const y=m==="all"?d.filter(C=>C.status==="pending"):d.filter(C=>C.status==="pending"&&C.categoryName===m);return y.length>0?s.jsx("div",{className:"space-y-2",children:y.map(C=>s.jsx(Ne,{children:s.jsx(Ee,{className:"p-3",children:s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"font-medium text-sm",children:C.title}),s.jsx(at,{variant:"secondary",className:"text-xs",children:"Pending"})]}),C.description&&s.jsx("p",{className:"text-xs text-muted-foreground",children:C.description}),C.filePath&&s.jsx("p",{className:"text-xs text-muted-foreground font-mono",children:C.filePath}),s.jsxs("div",{className:"text-xs text-muted-foreground",children:["Created: ",st(C.createdAt)]}),s.jsxs("div",{className:"flex gap-1 flex-wrap",children:[s.jsx(re,{size:"sm",className:"h-6 px-2 text-xs",disabled:O===C.id,onClick:()=>{D(C.id),k.approveRequest(C.id,"Approved"),setTimeout(()=>D(null),2e3)},children:O===C.id?"Processing...":"Approve"}),s.jsx(re,{variant:"outline",size:"sm",className:"h-6 px-2 text-xs",disabled:O===C.id,onClick:()=>{D(C.id),k.rejectRequest(C.id,"Rejected"),setTimeout(()=>D(null),2e3)},children:O===C.id?"Processing...":"Reject"}),s.jsx(re,{variant:"outline",size:"sm",className:"h-6 px-2 text-xs",disabled:O===C.id,onClick:()=>{D(C.id),k.requestRevisionRequest(C.id,"Needs revision"),setTimeout(()=>D(null),2e3)},children:O===C.id?"Processing...":"Request Revision"}),C.filePath&&s.jsx(re,{variant:"outline",size:"sm",className:"h-6 px-2 text-xs",onClick:()=>k.getApprovalContent(C.id),children:"Open in Editor"})]})]})})},C.id))}):s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:"No pending approvals for this specification"})})():s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:h.length<=1?"No documents with pending approvals found":"Select a category above to view pending approvals"})]}),s.jsxs(Ye,{value:"specs",className:"space-y-3",children:[s.jsxs("div",{className:"space-y-3",children:[s.jsx("div",{className:"flex items-center justify-center",children:s.jsxs("div",{className:"inline-flex items-center space-x-1 p-1 bg-muted rounded-md",children:[s.jsx(re,{variant:W==="active"?"default":"ghost",size:"sm",className:X("h-7 px-3 text-xs font-medium transition-all",W==="active"?"bg-primary text-primary-foreground shadow-sm":"hover:bg-muted-foreground/10"),onClick:()=>{pe("active"),Ce(null)},children:"Active"}),s.jsx(re,{variant:W==="archived"?"default":"ghost",size:"sm",className:X("h-7 px-3 text-xs font-medium transition-all",W==="archived"?"bg-primary text-primary-foreground shadow-sm":"hover:bg-muted-foreground/10"),onClick:()=>{pe("archived"),c(null)},children:"Archived"})]})}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("label",{className:"text-sm font-medium",children:"Specification:"}),s.jsxs(pt,{value:W==="active"?i||"":te||"",onValueChange:W==="active"?Y:Ce,children:[s.jsx(ht,{className:"w-full",children:s.jsx(mt,{placeholder:"Select a specification"})}),s.jsx(vt,{children:W==="active"?t.filter(y=>!y.isArchived).map(y=>s.jsx(Me,{value:y.name,children:y.displayName},y.name)):r.map(y=>s.jsx(Me,{value:y.name,children:y.displayName},y.name))})]}),W==="active"&&i&&s.jsx(re,{variant:"outline",size:"sm",className:"h-8 px-3 text-xs whitespace-nowrap",onClick:()=>k.archiveSpec(i),children:"Archive"}),W==="archived"&&te&&s.jsx(re,{variant:"outline",size:"sm",className:"h-8 px-3 text-xs whitespace-nowrap",onClick:()=>k.unarchiveSpec(te),children:"Unarchive"})]})]}),s.jsxs(Ne,{children:[s.jsx(rt,{className:"pb-2",children:s.jsx(ot,{className:"text-sm",children:"Specification Documents"})}),s.jsxs(Ee,{children:[(W==="active"?i:te)&&s.jsx("div",{className:"space-y-2",children:f.length>0?f.map(y=>s.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[s.jsxs("div",{className:"flex-1 space-y-1",children:[s.jsxs("div",{className:"font-medium text-sm",children:[s.jsx("span",{className:"capitalize",children:y.name}),".md"]}),y.exists&&y.lastModified&&s.jsxs("div",{className:"text-xs text-muted-foreground",children:["Modified ",st(y.lastModified)]}),!y.exists&&s.jsx("div",{className:"text-xs text-muted-foreground",children:"File not found"})]}),s.jsx(re,{size:"sm",className:"h-6 px-2 text-xs",disabled:!y.exists,onClick:()=>k.openDocument(W==="active"?i:te,y.name),children:"Open"})]},y.name)):s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:"No documents found for this specification"})}),!(W==="active"?i:te)&&s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:W==="active"?t.filter(y=>!y.isArchived).length===0?"No active specifications found":"Select a specification above to view documents":r.length===0?"No archived specifications found":"Select a specification above to view documents"})]})]})]}),s.jsx(Ye,{value:"steering",className:"space-y-3",children:s.jsxs(Ne,{children:[s.jsx(rt,{className:"pb-2",children:s.jsx(ot,{className:"text-sm",children:"Steering Documents"})}),s.jsx(Ee,{children:s.jsx("div",{className:"space-y-2",children:w.length>0?w.map(y=>s.jsxs("div",{className:"flex items-center justify-between p-2 border rounded",children:[s.jsxs("div",{className:"flex-1 space-y-1",children:[s.jsxs("div",{className:"font-medium text-sm",children:[s.jsx("span",{className:"capitalize",children:y.name}),".md"]}),y.exists&&y.lastModified&&s.jsxs("div",{className:"text-xs text-muted-foreground",children:["Modified ",st(y.lastModified)]}),!y.exists&&s.jsx("div",{className:"text-xs text-muted-foreground",children:"File not found"})]}),s.jsx(re,{size:"sm",className:"h-6 px-2 text-xs",disabled:!y.exists,onClick:()=>k.openSteeringDocument(y.name),children:"Open"})]},y.name)):s.jsx("div",{className:"text-center text-muted-foreground text-sm py-8",children:"No steering documents found"})})})]})})]}),F&&s.jsx(re,{className:"fixed bottom-4 right-4 z-20 rounded-full w-10 h-10 p-0 shadow-lg",onClick:K,title:"Scroll to top",children:s.jsx(Po,{className:"h-4 w-4"})})]})})}const Kn=document.getElementById("root");Kn&&(Fo.createRoot(Kn).render(s.jsx(Xc,{})),document.body.classList.add("loaded"));
